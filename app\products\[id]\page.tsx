"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRout<PERSON>, usePara<PERSON> } from "next/navigation"
import {
  ShoppingBag, User, LogOut, Package, Settings, Star, ShoppingCart, Heart,
  ChevronLeft, ChevronRight, Plus, Minus, Truck, Shield, RotateCcw,
  Share2, MessageCircle, ThumbsUp, ChevronDown, Home, ArrowLeft,
  Zap, Award, Clock, CheckCircle, AlertCircle, Info, TrendingUp
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

interface Category {
  id: string
  name: string
  slug: string
}

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice: number
  image: string
  images: string[]
  category: Category
  stock: number
  rating: number
  reviewCount: number
  soldCount: number
  featured: boolean
  createdAt: string
}

export default function ProductDetailPage() {
  const [user, setUser] = useState<User | null>(null)
  const [product, setProduct] = useState<Product | null>(null)
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([])
  const [quantity, setQuantity] = useState(1)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedImage, setSelectedImage] = useState(0)
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [activeTab, setActiveTab] = useState("description")
  const [reviewText, setReviewText] = useState("")
  const [reviewRating, setReviewRating] = useState(5)
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      try {
        setUser(JSON.parse(userData))
      } catch (error) {
        console.error('Error parsing user data:', error)
        localStorage.removeItem("user")
      }
    }

    const fetchProduct = async () => {
      try {
        setLoading(true)
        setError(null)

        const productId = params?.id
        if (!productId || typeof productId !== 'string') {
          throw new Error('Invalid product ID')
        }

        // Fetch product details
        const response = await fetch(`/api/products/${productId}`)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch product')
        }

        setProduct(data)

        // Fetch related products
        if (data.category) {
          try {
            const relatedResponse = await fetch(`/api/products?category=${data.category.id}&limit=4`)
            const relatedData = await relatedResponse.json()

            if (relatedResponse.ok && Array.isArray(relatedData)) {
              // Filter out current product from related products
              const filtered = relatedData.filter(p => p.id !== productId)
              setRelatedProducts(filtered.slice(0, 4))
            }
          } catch (relatedError) {
            console.error('Error fetching related products:', relatedError)
          }
        }
      } catch (error) {
        console.error('Error fetching product:', error)
        setError(error instanceof Error ? error.message : 'An error occurred')
        toast({
          title: "Lỗi",
          description: error instanceof Error ? error.message : 'Không thể tải thông tin sản phẩm',
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [params?.id, router, toast])

  const handleLogout = () => {
    localStorage.removeItem("user")
    setUser(null)
    router.push("/")
  }

  const handleQuantityChange = (value: number) => {
    if (value >= 1 && value <= (product?.stock || 1)) {
      setQuantity(value)
    }
  }

  const handleAddToCart = async () => {
    if (!user) {
      toast({
        title: "Yêu cầu đăng nhập",
        description: "Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng.",
        variant: "destructive",
      })
      router.push('/login')
      return
    }

    if (!product) {
      toast({
        title: "Lỗi",
        description: "Không thể thêm sản phẩm vào giỏ hàng.",
        variant: "destructive",
      })
      return
    }

    if (quantity > product.stock) {
      toast({
        title: "Không đủ hàng",
        description: `Chỉ còn ${product.stock} sản phẩm trong kho.`,
        variant: "destructive",
      })
      return
    }

    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          productId: product.id,
          quantity: quantity
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to add to cart')
      }

      toast({
        title: "Thành công",
        description: `Đã thêm ${quantity} sản phẩm vào giỏ hàng!`,
      })
      setQuantity(1)
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast({
        title: "Lỗi",
        description: error instanceof Error ? error.message : "Không thể thêm sản phẩm vào giỏ hàng. Vui lòng thử lại sau.",
        variant: "destructive",
      })
    }
  }

  const handleWishlistToggle = () => {
    setIsWishlisted(!isWishlisted)
    toast({
      title: isWishlisted ? "Đã xóa khỏi yêu thích" : "Đã thêm vào yêu thích",
      description: isWishlisted
        ? "Sản phẩm đã được xóa khỏi danh sách yêu thích"
        : "Sản phẩm đã được thêm vào danh sách yêu thích",
    })
  }

  const handleBuyNow = async () => {
    if (!user) {
      toast({
        title: "Yêu cầu đăng nhập",
        description: "Vui lòng đăng nhập để mua sản phẩm.",
        variant: "destructive",
      })
      router.push('/login')
      return
    }

    if (!product) {
      toast({
        title: "Lỗi",
        description: "Không thể mua sản phẩm này.",
        variant: "destructive",
      })
      return
    }

    if (quantity > product.stock) {
      toast({
        title: "Không đủ hàng",
        description: `Chỉ còn ${product.stock} sản phẩm trong kho.`,
        variant: "destructive",
      })
      return
    }

    try {
      // Add to cart first
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          productId: product.id,
          quantity: quantity
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to add to cart')
      }

      // Store a flag to indicate this is a "buy now" action
      sessionStorage.setItem('buyNowMode', 'true')

      // Redirect to cart page for immediate checkout
      router.push('/cart')
    } catch (error) {
      console.error('Error processing buy now:', error)
      toast({
        title: "Lỗi",
        description: "Không thể xử lý đơn hàng. Vui lòng thử lại sau.",
        variant: "destructive",
      })
    }
  }

  const handleShare = async () => {
    if (navigator.share && product) {
      try {
        await navigator.share({
          title: product.name,
          text: product.description,
          url: window.location.href,
        })
      } catch (error) {
        console.error('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Đã sao chép",
        description: "Đường dẫn sản phẩm đã được sao chép vào clipboard",
      })
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price)
  }

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { text: "Hết hàng", color: "destructive", icon: AlertCircle }
    if (stock < 10) return { text: `Chỉ còn ${stock} sản phẩm`, color: "warning", icon: AlertCircle }
    return { text: "Còn hàng", color: "success", icon: CheckCircle }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        {/* Header */}
        <header className="border-b bg-white sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-primary">
              TechStore
            </Link>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="grid gap-8 lg:grid-cols-2">
              <div className="space-y-4">
                <div className="aspect-square bg-gray-200 rounded-lg"></div>
                <div className="grid grid-cols-4 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="aspect-square bg-gray-200 rounded-lg"></div>
                  ))}
                </div>
              </div>
              <div className="space-y-6">
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-12 bg-gray-200 rounded w-1/3"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                  <div className="h-4 bg-gray-200 rounded w-4/6"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-background">
        {/* Header */}
        <header className="border-b bg-white sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-primary">
              TechStore
            </Link>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="text-center max-w-md">
              <div className="text-6xl mb-4">😞</div>
              <h2 className="text-2xl font-bold mb-2">Không tìm thấy sản phẩm</h2>
              <p className="text-muted-foreground mb-6">
                {error || "Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa."}
              </p>
              <div className="flex gap-4 justify-center">
                <Button variant="outline" onClick={() => router.back()}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Quay lại
                </Button>
                <Button asChild>
                  <Link href="/products">Xem tất cả sản phẩm</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const stockStatus = getStockStatus(product.stock)
  const discountPercentage = product.originalPrice > product.price
    ? Math.round((1 - product.price / product.originalPrice) * 100)
    : 0

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-primary">
            TechStore
          </Link>

          <div className="flex items-center space-x-4">
            <Button variant="outline" size="icon" asChild>
              <Link href="/cart">
                <ShoppingBag className="h-4 w-4" />
              </Link>
            </Button>

            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/avatars/01.png" alt={user.name} />
                      <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile">
                      <User className="mr-2 h-4 w-4" />
                      <span>Hồ sơ</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/orders">
                      <Package className="mr-2 h-4 w-4" />
                      <span>Đơn hàng</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Đăng xuất</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex space-x-2">
                <Button variant="ghost" asChild>
                  <Link href="/login">Đăng nhập</Link>
                </Button>
                <Button asChild>
                  <Link href="/register">Đăng ký</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">
                <Home className="h-4 w-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/products">Sản phẩm</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/products?category=${product.category.slug}`}>
                {product.category.name}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{product.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="grid gap-8 lg:grid-cols-2">
          {/* Enhanced Gallery */}
          <div className="space-y-4">
            <div className="relative aspect-square overflow-hidden rounded-lg bg-muted group">
              <img
                src={product.images?.[selectedImage] || product.image || '/placeholder.svg'}
                alt={product.name}
                className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder.svg';
                }}
              />
              {discountPercentage > 0 && (
                <Badge className="absolute top-4 right-4 bg-red-500 text-white">
                  -{discountPercentage}%
                </Badge>
              )}
              {product.featured && (
                <Badge className="absolute top-4 left-4 bg-primary">
                  <Award className="h-3 w-3 mr-1" />
                  Nổi bật
                </Badge>
              )}

              {/* Navigation arrows for mobile */}
              {product.images && product.images.length > 1 && (
                <>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => setSelectedImage(prev =>
                      prev === 0 ? product.images.length - 1 : prev - 1
                    )}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => setSelectedImage(prev =>
                      prev === product.images.length - 1 ? 0 : prev + 1
                    )}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}
            </div>

            {/* Thumbnail Gallery */}
            {product.images && product.images.length > 1 && (
              <div className="grid grid-cols-4 gap-4">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`relative aspect-square overflow-hidden rounded-lg bg-muted transition-all duration-200 ${
                      selectedImage === index
                        ? 'ring-2 ring-primary ring-offset-2'
                        : 'hover:ring-1 hover:ring-gray-300'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} - ${index + 1}`}
                      className="object-cover w-full h-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder.svg';
                      }}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Enhanced Product Info */}
          <div className="space-y-6">
            <div>
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h1 className="text-3xl font-bold tracking-tight mb-2">{product.name}</h1>
                  <p className="text-muted-foreground">{product.category.name}</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleWishlistToggle}
                    className={isWishlisted ? "text-red-500 border-red-500" : ""}
                  >
                    <Heart className={`h-4 w-4 ${isWishlisted ? "fill-current" : ""}`} />
                  </Button>
                  <Button variant="outline" size="icon" onClick={handleShare}>
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(product.rating)
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-lg font-medium">{product.rating}</span>
                <span className="text-muted-foreground">
                  ({product.reviewCount} đánh giá)
                </span>
                <Separator orientation="vertical" className="h-5" />
                <span className="text-muted-foreground flex items-center">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  Đã bán {product.soldCount}
                </span>
              </div>

              {/* Stock Status */}
              <div className="flex items-center gap-2 mb-4">
                <stockStatus.icon className={`h-4 w-4 ${
                  stockStatus.color === 'success' ? 'text-green-500' :
                  stockStatus.color === 'warning' ? 'text-yellow-500' :
                  'text-red-500'
                }`} />
                <span className={`text-sm font-medium ${
                  stockStatus.color === 'success' ? 'text-green-700' :
                  stockStatus.color === 'warning' ? 'text-yellow-700' :
                  'text-red-700'
                }`}>
                  {stockStatus.text}
                </span>
              </div>
            </div>

            {/* Pricing Section */}
            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
              <div className="flex items-baseline gap-3">
                <p className="text-4xl font-bold text-primary">{formatPrice(product.price)}</p>
                {product.originalPrice > product.price && (
                  <>
                    <p className="text-xl text-muted-foreground line-through">
                      {formatPrice(product.originalPrice)}
                    </p>
                    <Badge variant="destructive" className="text-sm">
                      Tiết kiệm {formatPrice(product.originalPrice - product.price)}
                    </Badge>
                  </>
                )}
              </div>

              {/* Quantity and Add to Cart */}
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <span className="font-medium">Số lượng:</span>
                  <div className="flex items-center border rounded-lg">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleQuantityChange(quantity - 1)}
                      disabled={quantity <= 1}
                      className="h-10 w-10"
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <Input
                      type="number"
                      min="1"
                      max={product.stock}
                      value={quantity}
                      onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                      className="w-20 text-center border-0 focus-visible:ring-0"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleQuantityChange(quantity + 1)}
                      disabled={quantity >= product.stock}
                      className="h-10 w-10"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    (Tối đa {product.stock} sản phẩm)
                  </span>
                </div>

                <div className="space-y-3">
                  <Button
                    size="lg"
                    className="w-full text-lg py-6"
                    onClick={handleAddToCart}
                    disabled={product.stock === 0}
                  >
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    {product.stock === 0 ? 'Hết hàng' : `Thêm vào giỏ hàng - ${formatPrice(product.price * quantity)}`}
                  </Button>

                  <Button
                    variant="outline"
                    size="lg"
                    className="w-full"
                    onClick={handleBuyNow}
                    disabled={product.stock === 0}
                  >
                    <Zap className="mr-2 h-5 w-5" />
                    {product.stock === 0 ? 'Hết hàng' : 'Mua ngay'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Delivery & Service Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-3 p-4 border rounded-lg">
                <Truck className="h-8 w-8 text-primary" />
                <div>
                  <p className="font-medium">Giao hàng nhanh</p>
                  <p className="text-sm text-muted-foreground">2-3 ngày làm việc</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 border rounded-lg">
                <Shield className="h-8 w-8 text-primary" />
                <div>
                  <p className="font-medium">Bảo hành chính hãng</p>
                  <p className="text-sm text-muted-foreground">12 tháng</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 border rounded-lg">
                <RotateCcw className="h-8 w-8 text-primary" />
                <div>
                  <p className="font-medium">Đổi trả miễn phí</p>
                  <p className="text-sm text-muted-foreground">Trong 7 ngày</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="description">Mô tả</TabsTrigger>
              <TabsTrigger value="specifications">Thông số</TabsTrigger>
              <TabsTrigger value="reviews">Đánh giá ({product.reviewCount})</TabsTrigger>
              <TabsTrigger value="qa">Hỏi đáp</TabsTrigger>
            </TabsList>

            <TabsContent value="description" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Mô tả sản phẩm</CardTitle>
                </CardHeader>
                <CardContent className="prose max-w-none">
                  <p className="text-muted-foreground leading-relaxed">
                    {product.description}
                  </p>
                  <div className="mt-6 space-y-4">
                    <h4 className="font-semibold">Tính năng nổi bật:</h4>
                    <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                      <li>Thiết kế hiện đại, sang trọng</li>
                      <li>Hiệu năng mạnh mẽ, ổn định</li>
                      <li>Chất lượng cao, bền bỉ</li>
                      <li>Hỗ trợ công nghệ tiên tiến</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="specifications" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Thông số kỹ thuật</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">Danh mục</span>
                        <span className="text-muted-foreground">{product.category.name}</span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">Thương hiệu</span>
                        <span className="text-muted-foreground">{product.name.split(' ')[0]}</span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">Tình trạng</span>
                        <Badge variant={product.stock > 0 ? "default" : "destructive"}>
                          {product.stock > 0 ? "Còn hàng" : "Hết hàng"}
                        </Badge>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">Đánh giá</span>
                        <span className="text-muted-foreground">{product.rating}/5 ⭐</span>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">Đã bán</span>
                        <span className="text-muted-foreground">{product.soldCount} sản phẩm</span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">Ngày ra mắt</span>
                        <span className="text-muted-foreground">
                          {new Date(product.createdAt).toLocaleDateString('vi-VN')}
                        </span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">Bảo hành</span>
                        <span className="text-muted-foreground">12 tháng</span>
                      </div>
                      <div className="flex justify-between py-2 border-b">
                        <span className="font-medium">Xuất xứ</span>
                        <span className="text-muted-foreground">Chính hãng</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews" className="mt-6">
              <div className="space-y-6">
                {/* Review Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Đánh giá từ khách hàng</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div className="text-center">
                        <div className="text-4xl font-bold mb-2">{product.rating}</div>
                        <div className="flex justify-center mb-2">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-5 w-5 ${
                                i < Math.floor(product.rating)
                                  ? "text-yellow-400 fill-yellow-400"
                                  : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                        <p className="text-muted-foreground">{product.reviewCount} đánh giá</p>
                      </div>
                      <div className="space-y-2">
                        {[5, 4, 3, 2, 1].map(rating => (
                          <div key={rating} className="flex items-center gap-2">
                            <span className="text-sm w-8">{rating}⭐</span>
                            <Progress value={rating === 5 ? 70 : rating === 4 ? 20 : 5} className="flex-1" />
                            <span className="text-sm text-muted-foreground w-12">
                              {rating === 5 ? '70%' : rating === 4 ? '20%' : '5%'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Sample Reviews */}
                <div className="space-y-4">
                  {[1, 2, 3].map(i => (
                    <Card key={i}>
                      <CardContent className="pt-6">
                        <div className="flex items-start gap-4">
                          <Avatar>
                            <AvatarFallback>U{i}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="font-medium">Người dùng {i}</span>
                              <div className="flex">
                                {[...Array(5)].map((_, j) => (
                                  <Star
                                    key={j}
                                    className="h-4 w-4 text-yellow-400 fill-yellow-400"
                                  />
                                ))}
                              </div>
                              <span className="text-sm text-muted-foreground">
                                {i} ngày trước
                              </span>
                            </div>
                            <p className="text-muted-foreground">
                              Sản phẩm rất tốt, chất lượng như mong đợi. Giao hàng nhanh, đóng gói cẩn thận.
                            </p>
                            <div className="flex items-center gap-4 mt-3">
                              <Button variant="ghost" size="sm">
                                <ThumbsUp className="h-4 w-4 mr-1" />
                                Hữu ích (12)
                              </Button>
                              <Button variant="ghost" size="sm">
                                <MessageCircle className="h-4 w-4 mr-1" />
                                Trả lời
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Write Review */}
                <Card>
                  <CardHeader>
                    <CardTitle>Viết đánh giá</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Đánh giá của bạn</label>
                      <div className="flex gap-1">
                        {[1, 2, 3, 4, 5].map(rating => (
                          <Button
                            key={rating}
                            variant="ghost"
                            size="sm"
                            onClick={() => setReviewRating(rating)}
                          >
                            <Star
                              className={`h-5 w-5 ${
                                rating <= reviewRating
                                  ? "text-yellow-400 fill-yellow-400"
                                  : "text-gray-300"
                              }`}
                            />
                          </Button>
                        ))}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Nội dung đánh giá</label>
                      <Textarea
                        placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm..."
                        value={reviewText}
                        onChange={(e) => setReviewText(e.target.value)}
                        rows={4}
                      />
                    </div>
                    <Button>Gửi đánh giá</Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="qa" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Hỏi đáp về sản phẩm</CardTitle>
                  <CardDescription>
                    Đặt câu hỏi để được tư vấn chi tiết về sản phẩm
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex gap-4">
                      <Textarea placeholder="Đặt câu hỏi về sản phẩm..." />
                      <Button>Gửi</Button>
                    </div>
                    <Separator />
                    <div className="text-center py-8 text-muted-foreground">
                      Chưa có câu hỏi nào. Hãy là người đầu tiên đặt câu hỏi!
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold mb-6">Sản phẩm liên quan</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <Card key={relatedProduct.id} className="group hover:shadow-lg transition-shadow">
                  <CardHeader className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <img
                        src={relatedProduct.image || '/placeholder.svg'}
                        alt={relatedProduct.name}
                        className="aspect-square object-cover transition-transform duration-300 group-hover:scale-105"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder.svg';
                        }}
                      />
                    </div>
                    <div className="p-4">
                      <CardTitle className="line-clamp-2 text-lg">
                        {relatedProduct.name}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center gap-1 mb-3">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(relatedProduct.rating)
                                ? "text-yellow-400 fill-yellow-400"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-muted-foreground">
                        ({relatedProduct.reviewCount})
                      </span>
                    </div>

                    <div className="flex items-center gap-2 mb-4">
                      <p className="text-lg font-bold text-primary">
                        {formatPrice(relatedProduct.price)}
                      </p>
                      {relatedProduct.originalPrice > relatedProduct.price && (
                        <p className="text-sm text-muted-foreground line-through">
                          {formatPrice(relatedProduct.originalPrice)}
                        </p>
                      )}
                    </div>

                    <Button className="w-full" asChild>
                      <Link href={`/products/${relatedProduct.id}`}>
                        Xem chi tiết
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 