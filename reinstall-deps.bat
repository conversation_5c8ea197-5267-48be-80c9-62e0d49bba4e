@echo off
cd /d "d:\tech-store-thien"
echo Current directory: %CD%
echo Removing node_modules...
rmdir /s /q node_modules 2>nul
echo Removing package-lock.json...
del package-lock.json 2>nul
echo Installing dependencies...
npm install
echo Installation complete.
echo Testing better-sqlite3...
node -e "try { const db = require('better-sqlite3'); console.log('SUCCESS: better-sqlite3 is available'); } catch(e) { console.log('ERROR:', e.message); }"
pause
