// Simple migration script
const fs = require('fs');
const path = require('path');

console.log('Starting simple migration...');

try {
  // Read categories
  const categoriesPath = path.join(process.cwd(), 'data', 'categories.json');
  console.log('Reading categories from:', categoriesPath);
  
  const categoriesRaw = fs.readFileSync(categoriesPath, 'utf-8');
  const categories = JSON.parse(categoriesRaw);
  console.log('Found', categories.length, 'categories');
  
  // Read users
  const usersPath = path.join(process.cwd(), 'data', 'users.json');
  console.log('Reading users from:', usersPath);
  
  const usersRaw = fs.readFileSync(usersPath, 'utf-8');
  const users = JSON.parse(usersRaw);
  console.log('Found', users.length, 'users');
  
  // Create database structure
  const db = {
    categories: categories.map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      description: cat.description,
      image: cat.image,
      status: cat.status || 'active',
      created_at: cat.createdAt || new Date().toISOString()
    })),
    users: users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      password: user.password,
      phone: user.phone,
      role: user.role || 'customer',
      status: user.status || 'active',
      created_at: user.createdAt || new Date().toISOString()
    })),
    user_addresses: [],
    products: [],
    reviews: [],
    orders: [],
    order_items: [],
    order_shipping_addresses: [],
    transactions: [],
    _meta: {
      created: new Date().toISOString(),
      version: '1.0.0',
      lastMigration: new Date().toISOString()
    }
  };
  
  // Add user addresses
  users.forEach(user => {
    if (user.address) {
      db.user_addresses.push({
        id: db.user_addresses.length + 1,
        user_id: user.id,
        full_name: user.address.fullName,
        phone: user.address.phone,
        address: user.address.address,
        city: user.address.city,
        district: user.address.district,
        ward: user.address.ward,
        is_default: true,
        created_at: user.createdAt || new Date().toISOString()
      });
    }
  });
  
  console.log('Created database with:');
  console.log('- Categories:', db.categories.length);
  console.log('- Users:', db.users.length);
  console.log('- User addresses:', db.user_addresses.length);
  
  // Save to database file
  const dbPath = path.join(process.cwd(), 'data', 'store.json');
  fs.writeFileSync(dbPath, JSON.stringify(db, null, 2));
  console.log('Saved database to:', dbPath);
  
  console.log('Migration completed successfully!');
  
} catch (error) {
  console.error('Migration failed:', error.message);
  console.error('Stack:', error.stack);
}
