"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ChevronRight, Minus, Plus, ShoppingBag, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

interface CartItem {
  id: string
  productId: string
  product: {
    id: string
    name: string
    price: number
    originalPrice?: number
    image: string
    stock: number
    category: {
      id: string
      name: string
    }
  }
  quantity: number
  createdAt: string
  updatedAt: string
}

export default function CartPage() {
  const [user, setUser] = useState<User | null>(null)
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData)
        setUser(parsedUser)
        fetchCart(parsedUser.id)
      } catch (error) {
        console.error('Error parsing user data:', error)
        localStorage.removeItem("user")
        router.push('/login')
      }
    } else {
      router.push('/login')
    }
  }, [router])

  const fetchCart = async (userId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/cart?userId=${userId}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch cart')
      }

      setCartItems(data.items || [])
    } catch (error) {
      console.error('Error fetching cart:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải giỏ hàng. Vui lòng thử lại sau.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateQuantity = async (id: string, newQuantity: number) => {
    if (newQuantity < 1 || !user) return

    const item = cartItems.find((item) => item.id === id)
    if (!item) return

    if (newQuantity > item.product.stock) {
      toast({
        title: "Số lượng không hợp lệ",
        description: `Chỉ còn ${item.product.stock} sản phẩm trong kho.`,
        variant: "destructive",
      })
      return
    }

    try {
      setUpdating(id)
      const response = await fetch('/api/cart', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cartItemId: id,
          quantity: newQuantity,
          userId: user.id
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update quantity')
      }

      // Update local state
      setCartItems(prev => prev.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      ))

      toast({
        title: "Đã cập nhật",
        description: "Số lượng sản phẩm đã được cập nhật.",
      })
    } catch (error) {
      console.error('Error updating quantity:', error)
      toast({
        title: "Lỗi",
        description: error instanceof Error ? error.message : "Không thể cập nhật số lượng.",
        variant: "destructive",
      })
    } finally {
      setUpdating(null)
    }
  }

  const removeItem = async (id: string) => {
    if (!user) return

    try {
      const response = await fetch(`/api/cart?cartItemId=${id}&userId=${user.id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to remove item')
      }

      // Update local state
      setCartItems(prev => prev.filter(item => item.id !== id))

      toast({
        title: "Đã xóa sản phẩm",
        description: "Sản phẩm đã được xóa khỏi giỏ hàng.",
      })
    } catch (error) {
      console.error('Error removing item:', error)
      toast({
        title: "Lỗi",
        description: error instanceof Error ? error.message : "Không thể xóa sản phẩm.",
        variant: "destructive",
      })
    }
  }

  const subtotal = cartItems.reduce((sum, item) => sum + item.product.price * item.quantity, 0)
  const shipping = subtotal > 500000 ? 0 : 30000
  const total = subtotal + shipping

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <header className="sticky top-0 z-40 w-full border-b bg-background">
          <div className="container flex h-16 items-center">
            <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
              <ShoppingBag className="h-6 w-6" />
              <span>TechStore</span>
            </Link>
          </div>
        </header>
        <main className="flex-1">
          <div className="container py-10">
            <div className="flex items-center justify-center min-h-[60vh]">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
            <ShoppingBag className="h-6 w-6" />
            <span>TechStore</span>
          </Link>
          <div className="ml-auto flex items-center gap-4">
            {user ? (
              <span className="text-sm text-muted-foreground">
                Xin chào, {user.name}
              </span>
            ) : (
              <>
                <Link href="/login">
                  <Button variant="outline">Đăng nhập</Button>
                </Link>
                <Link href="/register">
                  <Button>Đăng ký</Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </header>
      <main className="flex-1">
        <div className="container py-10">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-6">
            <Link href="/" className="hover:text-foreground">
              Trang chủ
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground">Giỏ hàng</span>
          </div>

          <h1 className="text-3xl font-bold mb-8">Giỏ hàng của bạn</h1>

          {cartItems.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground" />
              <h2 className="text-xl font-semibold mt-4">Giỏ hàng của bạn đang trống</h2>
              <p className="text-muted-foreground mt-2">Hãy thêm sản phẩm vào giỏ hàng để tiếp tục.</p>
              <Button asChild className="mt-6">
                <Link href="/products">Tiếp tục mua sắm</Link>
              </Button>
            </div>
          ) : (
            <div className="grid gap-8 md:grid-cols-3">
              <div className="md:col-span-2">
                <div className="rounded-lg border">
                  <div className="p-6">
                    <h2 className="text-lg font-semibold">Sản phẩm ({cartItems.length})</h2>
                  </div>
                  <Separator />
                  {cartItems.map((item) => (
                    <div key={item.id} className="p-6">
                      <div className="flex gap-4">
                        <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border">
                          <img
                            src={item.product.image || "/placeholder.svg"}
                            alt={item.product.name}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <div className="flex flex-1 flex-col">
                          <div className="flex justify-between">
                            <Link href={`/products/${item.product.id}`} className="hover:underline">
                              <h3 className="font-medium">{item.product.name}</h3>
                            </Link>
                            <p className="font-medium">{formatCurrency(item.product.price * item.quantity)}</p>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {formatCurrency(item.product.price)} / sản phẩm
                          </p>
                          {item.product.stock < 10 && (
                            <p className="text-sm text-orange-600">
                              Chỉ còn {item.product.stock} sản phẩm
                            </p>
                          )}
                          <div className="mt-auto flex items-center justify-between">
                            <div className="flex items-center">
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8 rounded-r-none"
                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                disabled={updating === item.id || item.quantity <= 1}
                              >
                                <Minus className="h-3 w-3" />
                                <span className="sr-only">Giảm</span>
                              </Button>
                              <div className="flex h-8 w-12 items-center justify-center border-y">
                                {updating === item.id ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b border-primary"></div>
                                ) : (
                                  item.quantity
                                )}
                              </div>
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8 rounded-l-none"
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                disabled={updating === item.id || item.quantity >= item.product.stock}
                              >
                                <Plus className="h-3 w-3" />
                                <span className="sr-only">Tăng</span>
                              </Button>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeItem(item.id)}
                              disabled={updating === item.id}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Xóa</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                      <Separator className="mt-6" />
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <div className="rounded-lg border">
                  <div className="p-6">
                    <h2 className="text-lg font-semibold">Tóm tắt đơn hàng</h2>
                    <div className="mt-6 space-y-4">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Tạm tính</span>
                        <span>{formatCurrency(subtotal)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Phí vận chuyển</span>
                        <span>{shipping === 0 ? "Miễn phí" : formatCurrency(shipping)}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between font-medium">
                        <span>Tổng cộng</span>
                        <span>{formatCurrency(total)}</span>
                      </div>
                      <Button className="w-full" asChild>
                        <Link href="/checkout">Thanh toán</Link>
                      </Button>
                      <Button variant="outline" className="w-full" asChild>
                        <Link href="/products">Tiếp tục mua sắm</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
      <footer className="w-full border-t bg-background">
        <div className="container py-6">
          <p className="text-center text-sm text-muted-foreground">© 2025 TechStore. Tất cả quyền được bảo lưu.</p>
        </div>
      </footer>
    </div>
  )
}
