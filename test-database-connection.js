// Test database connection and basic operations
const { getDatabase } = require('./lib/database/connection');
const { dbUtils } = require('./lib/database/utils');

console.log('🧪 Testing database connection and operations...\n');

try {
  // Test database connection
  const db = getDatabase();
  console.log('✓ Database connection established');
  console.log('Database type:', db._isFileBased ? 'File-based' : 'SQLite');
  
  // Test basic operations
  console.log('\n📝 Testing basic CRUD operations...');
  
  // Test insert
  console.log('\n1. Testing INSERT operation...');
  const testCategory = {
    id: 'test-category-1',
    name: 'Test Category',
    slug: 'test-category',
    description: 'A test category',
    status: 'active'
  };
  
  const insertedCategory = dbUtils.insert('categories', testCategory);
  console.log('✓ Insert successful:', insertedCategory.name);
  
  // Test select
  console.log('\n2. Testing SELECT operation...');
  const categories = dbUtils.select('categories', { status: 'active' });
  console.log('✓ Select successful, found', categories.length, 'active categories');
  
  // Test update
  console.log('\n3. Testing UPDATE operation...');
  const updatedCategory = dbUtils.update('categories', 
    { description: 'Updated test category' }, 
    { id: 'test-category-1' }
  );
  console.log('✓ Update successful:', updatedCategory ? 'Updated' : 'Not found');
  
  // Test count
  console.log('\n4. Testing COUNT operation...');
  const categoryCount = dbUtils.count('categories');
  console.log('✓ Count successful, total categories:', categoryCount);
  
  // Test delete
  console.log('\n5. Testing DELETE operation...');
  const deleted = dbUtils.delete('categories', { id: 'test-category-1' });
  console.log('✓ Delete successful:', deleted ? 'Deleted' : 'Not found');
  
  console.log('\n🎉 All database operations completed successfully!');
  
} catch (error) {
  console.error('❌ Database test failed:', error);
  console.error('Error details:', error.message);
  process.exit(1);
}
