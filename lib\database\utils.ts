import { getDatabase } from './connection';

// Generic database utilities

export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

export class DatabaseUtils {
  private db: any;

  constructor() {
    this.db = getDatabase();
  }

  // Check if we're using file-based database
  private isFileBased(): boolean {
    return this.db && this.db._isFileBased === true;
  }

  // Generic select with pagination and ordering
  select<T>(
    table: string,
    where: Record<string, any> = {},
    options: QueryOptions = {}
  ): T[] {
    if (this.isFileBased()) {
      return this.fileBasedSelect<T>(table, where, options);
    }

    const { limit, offset, orderBy, orderDirection = 'ASC' } = options;

    let query = `SELECT * FROM ${table}`;
    const params: any[] = [];

    // Build WHERE clause
    const whereConditions = Object.entries(where).map(([key, value]) => {
      params.push(value);
      return `${key} = ?`;
    });

    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    // Add ORDER BY
    if (orderBy) {
      query += ` ORDER BY ${orderBy} ${orderDirection}`;
    }

    // Add LIMIT and OFFSET
    if (limit) {
      query += ` LIMIT ${limit}`;
      if (offset) {
        query += ` OFFSET ${offset}`;
      }
    }

    const stmt = this.db.prepare(query);
    return stmt.all(...params) as T[];
  }

  // File-based select implementation
  private fileBasedSelect<T>(
    table: string,
    where: Record<string, any> = {},
    options: QueryOptions = {}
  ): T[] {
    const data = this.db._loadData();
    let results = data[table] || [];

    // Apply WHERE conditions
    if (Object.keys(where).length > 0) {
      results = results.filter((item: any) => {
        return Object.entries(where).every(([key, value]) => {
          return item[key] === value;
        });
      });
    }

    // Apply ordering
    if (options.orderBy) {
      results.sort((a: any, b: any) => {
        const aVal = a[options.orderBy!];
        const bVal = b[options.orderBy!];
        const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        return options.orderDirection === 'DESC' ? -comparison : comparison;
      });
    }

    // Apply pagination
    if (options.offset) {
      results = results.slice(options.offset);
    }
    if (options.limit) {
      results = results.slice(0, options.limit);
    }

    return results as T[];
  }

  // Generic insert
  insert<T>(table: string, data: Record<string, any>): T {
    if (this.isFileBased()) {
      return this.fileBasedInsert<T>(table, data);
    }

    const columns = Object.keys(data);
    const placeholders = columns.map(() => '?').join(', ');
    const values = Object.values(data);

    const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
    const stmt = this.db.prepare(query);

    const result = stmt.run(...values);

    // Return the inserted record
    const selectStmt = this.db.prepare(`SELECT * FROM ${table} WHERE rowid = ?`);
    return selectStmt.get(result.lastInsertRowid) as T;
  }

  // File-based insert implementation
  private fileBasedInsert<T>(table: string, data: Record<string, any>): T {
    const dbData = this.db._loadData();

    if (!dbData[table]) {
      dbData[table] = [];
    }

    // Add auto-increment ID if not provided
    if (!data.id && table !== 'user_addresses' && table !== 'order_shipping_addresses') {
      data.id = `${table}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    } else if ((table === 'user_addresses' || table === 'order_shipping_addresses') && !data.id) {
      data.id = dbData[table].length + 1;
    }

    // Add created_at if not provided
    if (!data.created_at) {
      data.created_at = new Date().toISOString();
    }

    dbData[table].push(data);
    this.db._saveData(dbData);

    return data as T;
  }

  // Generic update
  update<T>(
    table: string,
    data: Record<string, any>,
    where: Record<string, any>
  ): T | null {
    if (this.isFileBased()) {
      return this.fileBasedUpdate<T>(table, data, where);
    }

    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');

    const query = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
    const stmt = this.db.prepare(query);

    const params = [...Object.values(data), ...Object.values(where)];
    const result = stmt.run(...params);

    if (result.changes === 0) {
      return null;
    }

    // Return the updated record
    const selectStmt = this.db.prepare(`SELECT * FROM ${table} WHERE ${whereClause}`);
    return selectStmt.get(...Object.values(where)) as T;
  }

  // File-based update implementation
  private fileBasedUpdate<T>(
    table: string,
    data: Record<string, any>,
    where: Record<string, any>
  ): T | null {
    const dbData = this.db._loadData();

    if (!dbData[table]) {
      return null;
    }

    const index = dbData[table].findIndex((item: any) => {
      return Object.entries(where).every(([key, value]) => {
        return item[key] === value;
      });
    });

    if (index === -1) {
      return null;
    }

    // Update the record
    dbData[table][index] = { ...dbData[table][index], ...data };
    this.db._saveData(dbData);

    return dbData[table][index] as T;
  }

  // Generic delete
  delete(table: string, where: Record<string, any>): boolean {
    if (this.isFileBased()) {
      return this.fileBasedDelete(table, where);
    }

    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
    const query = `DELETE FROM ${table} WHERE ${whereClause}`;
    const stmt = this.db.prepare(query);

    const result = stmt.run(...Object.values(where));
    return result.changes > 0;
  }

  // File-based delete implementation
  private fileBasedDelete(table: string, where: Record<string, any>): boolean {
    const dbData = this.db._loadData();

    if (!dbData[table]) {
      return false;
    }

    const initialLength = dbData[table].length;
    dbData[table] = dbData[table].filter((item: any) => {
      return !Object.entries(where).every(([key, value]) => {
        return item[key] === value;
      });
    });

    const deleted = dbData[table].length < initialLength;
    if (deleted) {
      this.db._saveData(dbData);
    }

    return deleted;
  }

  // Count records
  count(table: string, where: Record<string, any> = {}): number {
    if (this.isFileBased()) {
      const results = this.fileBasedSelect(table, where, {});
      return results.length;
    }

    let query = `SELECT COUNT(*) as count FROM ${table}`;
    const params: any[] = [];

    const whereConditions = Object.entries(where).map(([key, value]) => {
      params.push(value);
      return `${key} = ?`;
    });

    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    const stmt = this.db.prepare(query);
    const result = stmt.get(...params) as { count: number };
    return result.count;
  }

  // Check if record exists
  exists(table: string, where: Record<string, any>): boolean {
    return this.count(table, where) > 0;
  }

  // Execute raw SQL
  raw<T>(query: string, params: any[] = []): T[] {
    if (this.isFileBased()) {
      console.warn('Raw SQL not supported in file-based mode:', query);
      return [];
    }

    const stmt = this.db.prepare(query);
    return stmt.all(...params) as T[];
  }

  // Execute raw SQL and get single result
  rawOne<T>(query: string, params: any[] = []): T | null {
    if (this.isFileBased()) {
      console.warn('Raw SQL not supported in file-based mode:', query);
      return null;
    }

    const stmt = this.db.prepare(query);
    return (stmt.get(...params) as T) || null;
  }

  // Execute raw SQL without return
  exec(query: string, params: any[] = []): void {
    if (this.isFileBased()) {
      console.warn('Raw SQL not supported in file-based mode:', query);
      return;
    }

    const stmt = this.db.prepare(query);
    stmt.run(...params);
  }
}

// Export singleton instance
export const dbUtils = new DatabaseUtils();
