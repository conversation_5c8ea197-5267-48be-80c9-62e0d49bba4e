"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import {
  ChevronRight, CreditCard, Truck, MapPin, User, Phone,
  Edit, Check, AlertCircle, ArrowLeft, ShoppingBag
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

interface CartItem {
  id: string
  productId: string
  product: {
    id: string
    name: string
    price: number
    originalPrice?: number
    image: string
    stock: number
    category: {
      id: string
      name: string
    }
  }
  quantity: number
}

interface ShippingAddress {
  fullName: string
  phone: string
  address: string
  city: string
  district: string
  ward: string
}

export default function CheckoutPage() {
  const [user, setUser] = useState<User | null>(null)
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState("cod")
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
    fullName: "",
    phone: "",
    address: "",
    city: "",
    district: "",
    ward: ""
  })
  const [editingAddress, setEditingAddress] = useState(false)
  const [orderNotes, setOrderNotes] = useState("")

  const router = useRouter()

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (!userData) {
      router.push('/login')
      return
    }

    try {
      const parsedUser = JSON.parse(userData)
      setUser(parsedUser)

      // Set default shipping address from user profile
      if (parsedUser.address) {
        setShippingAddress(parsedUser.address)
      }

      // Check if this is a "buy now" checkout
      const buyNowMode = sessionStorage.getItem('buyNowMode')
      if (buyNowMode === 'true') {
        // Load cart items (buy now adds to cart first)
        fetchCart(parsedUser.id)
        // Clear the buy now flag
        sessionStorage.removeItem('buyNowMode')
      } else {
        // Regular cart checkout
        fetchCart(parsedUser.id)
      }
    } catch (error) {
      console.error('Error parsing user data:', error)
      localStorage.removeItem("user")
      router.push('/login')
    }
  }, [router])

  const fetchCart = async (userId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/cart?userId=${userId}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch cart')
      }

      if (!data.items || data.items.length === 0) {
        toast({
          title: "Giỏ hàng trống",
          description: "Không có sản phẩm nào để thanh toán.",
          variant: "destructive",
        })
        router.push('/cart')
        return
      }

      setCartItems(data.items)
    } catch (error) {
      console.error('Error fetching cart:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải thông tin giỏ hàng. Vui lòng thử lại sau.",
        variant: "destructive",
      })
      router.push('/cart')
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return price.toLocaleString('vi-VN') + ' ₫'
  }

  const subtotal = cartItems.reduce((sum, item) => sum + item.product.price * item.quantity, 0)
  const shipping = subtotal > 500000 ? 0 : 30000
  const total = subtotal + shipping

  const validateForm = () => {
    if (!shippingAddress.fullName.trim()) {
      toast({
        title: "Thông tin thiếu",
        description: "Vui lòng nhập họ tên người nhận.",
        variant: "destructive",
      })
      return false
    }

    if (!shippingAddress.phone.trim()) {
      toast({
        title: "Thông tin thiếu",
        description: "Vui lòng nhập số điện thoại người nhận.",
        variant: "destructive",
      })
      return false
    }

    if (!shippingAddress.address.trim()) {
      toast({
        title: "Thông tin thiếu",
        description: "Vui lòng nhập địa chỉ giao hàng.",
        variant: "destructive",
      })
      return false
    }

    if (!shippingAddress.city.trim()) {
      toast({
        title: "Thông tin thiếu",
        description: "Vui lòng nhập thành phố.",
        variant: "destructive",
      })
      return false
    }

    return true
  }

  const handleSubmitOrder = async () => {
    if (!user || !validateForm()) return

    try {
      setSubmitting(true)

      const orderData = {
        id: `order-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        userId: user.id,
        items: cartItems.map(item => ({
          id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          productId: item.product.id,
          product: {
            id: item.product.id,
            name: item.product.name,
            price: item.product.price,
            image: item.product.image
          },
          quantity: item.quantity,
          price: item.product.price
        })),
        totalAmount: total,
        status: 'pending',
        paymentMethod: paymentMethod,
        shippingAddress: shippingAddress,
        notes: orderNotes
      }

      // Create order
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create order')
      }

      // Clear cart after successful order
      await fetch(`/api/cart/clear?userId=${user.id}`, {
        method: 'DELETE'
      })

      toast({
        title: "Đặt hàng thành công!",
        description: `Đơn hàng ${data.id} đã được tạo thành công.`,
      })

      // Redirect to order confirmation
      router.push(`/order/${data.id}`)
    } catch (error) {
      console.error('Error creating order:', error)
      toast({
        title: "Lỗi đặt hàng",
        description: error instanceof Error ? error.message : "Không thể tạo đơn hàng. Vui lòng thử lại sau.",
        variant: "destructive",
      })
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <header className="border-b bg-white sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-primary">
              TechStore
            </Link>
          </div>
        </header>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-primary">
            TechStore
          </Link>
          <div className="flex items-center gap-4">
            <span className="text-sm text-muted-foreground">
              Xin chào, {user?.name}
            </span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center gap-1 text-sm text-muted-foreground mb-6">
          <Link href="/" className="hover:text-foreground">
            Trang chủ
          </Link>
          <ChevronRight className="h-4 w-4" />
          <Link href="/cart" className="hover:text-foreground">
            Giỏ hàng
          </Link>
          <ChevronRight className="h-4 w-4" />
          <span className="text-foreground">Thanh toán</span>
        </div>

        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" size="sm" asChild>
            <Link href="/cart">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại giỏ hàng
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">Thanh toán đơn hàng</h1>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Left Column - Forms */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingBag className="h-5 w-5" />
                  Thông tin sản phẩm
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex gap-4 p-4 border rounded-lg">
                      <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border">
                        <img
                          src={item.product.image || "/placeholder.svg"}
                          alt={item.product.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{item.product.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {formatPrice(item.product.price)} x {item.quantity}
                        </p>
                        <p className="font-medium text-primary">
                          {formatPrice(item.product.price * item.quantity)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Địa chỉ giao hàng
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingAddress(!editingAddress)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {editingAddress ? 'Hủy' : 'Chỉnh sửa'}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {editingAddress ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="fullName">Họ và tên *</Label>
                        <Input
                          id="fullName"
                          value={shippingAddress.fullName}
                          onChange={(e) => setShippingAddress(prev => ({
                            ...prev,
                            fullName: e.target.value
                          }))}
                          placeholder="Nhập họ và tên"
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone">Số điện thoại *</Label>
                        <Input
                          id="phone"
                          value={shippingAddress.phone}
                          onChange={(e) => setShippingAddress(prev => ({
                            ...prev,
                            phone: e.target.value
                          }))}
                          placeholder="Nhập số điện thoại"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="address">Địa chỉ *</Label>
                      <Input
                        id="address"
                        value={shippingAddress.address}
                        onChange={(e) => setShippingAddress(prev => ({
                          ...prev,
                          address: e.target.value
                        }))}
                        placeholder="Nhập địa chỉ chi tiết"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="city">Thành phố *</Label>
                        <Input
                          id="city"
                          value={shippingAddress.city}
                          onChange={(e) => setShippingAddress(prev => ({
                            ...prev,
                            city: e.target.value
                          }))}
                          placeholder="Thành phố"
                        />
                      </div>
                      <div>
                        <Label htmlFor="district">Quận/Huyện</Label>
                        <Input
                          id="district"
                          value={shippingAddress.district}
                          onChange={(e) => setShippingAddress(prev => ({
                            ...prev,
                            district: e.target.value
                          }))}
                          placeholder="Quận/Huyện"
                        />
                      </div>
                      <div>
                        <Label htmlFor="ward">Phường/Xã</Label>
                        <Input
                          id="ward"
                          value={shippingAddress.ward}
                          onChange={(e) => setShippingAddress(prev => ({
                            ...prev,
                            ward: e.target.value
                          }))}
                          placeholder="Phường/Xã"
                        />
                      </div>
                    </div>
                    <Button
                      onClick={() => setEditingAddress(false)}
                      className="w-full"
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Lưu địa chỉ
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{shippingAddress.fullName}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{shippingAddress.phone}</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <div>
                        <p>{shippingAddress.address}</p>
                        <p className="text-sm text-muted-foreground">
                          {[shippingAddress.ward, shippingAddress.district, shippingAddress.city]
                            .filter(Boolean)
                            .join(', ')}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Method */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Phương thức thanh toán
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
                  <div className="flex items-center space-x-2 p-4 border rounded-lg">
                    <RadioGroupItem value="cod" id="cod" />
                    <Label htmlFor="cod" className="flex-1 cursor-pointer">
                      <div className="flex items-center gap-3">
                        <Truck className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Thanh toán khi nhận hàng (COD)</p>
                          <p className="text-sm text-muted-foreground">
                            Thanh toán bằng tiền mặt khi nhận hàng
                          </p>
                        </div>
                      </div>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 p-4 border rounded-lg">
                    <RadioGroupItem value="bank_transfer" id="bank_transfer" />
                    <Label htmlFor="bank_transfer" className="flex-1 cursor-pointer">
                      <div className="flex items-center gap-3">
                        <CreditCard className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Chuyển khoản ngân hàng</p>
                          <p className="text-sm text-muted-foreground">
                            Chuyển khoản trước khi giao hàng
                          </p>
                        </div>
                      </div>
                    </Label>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>

            {/* Order Notes */}
            <Card>
              <CardHeader>
                <CardTitle>Ghi chú đơn hàng</CardTitle>
                <CardDescription>
                  Thêm ghi chú cho đơn hàng (tùy chọn)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={orderNotes}
                  onChange={(e) => setOrderNotes(e.target.value)}
                  placeholder="Nhập ghi chú cho đơn hàng..."
                  rows={3}
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Order Summary */}
          <div>
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle>Tóm tắt đơn hàng</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Tạm tính ({cartItems.length} sản phẩm)</span>
                    <span>{formatPrice(subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phí vận chuyển</span>
                    <span>{shipping === 0 ? "Miễn phí" : formatPrice(shipping)}</span>
                  </div>
                  {shipping === 0 && (
                    <p className="text-xs text-green-600">
                      🎉 Miễn phí vận chuyển cho đơn hàng trên {formatPrice(500000)}
                    </p>
                  )}
                  <Separator />
                  <div className="flex justify-between text-lg font-semibold">
                    <span>Tổng cộng</span>
                    <span className="text-primary">{formatPrice(total)}</span>
                  </div>
                </div>

                <div className="space-y-3 pt-4">
                  <Button
                    onClick={handleSubmitOrder}
                    disabled={submitting || cartItems.length === 0}
                    className="w-full text-lg py-6"
                    size="lg"
                  >
                    {submitting ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Đang xử lý...
                      </div>
                    ) : (
                      `Đặt hàng - ${formatPrice(total)}`
                    )}
                  </Button>

                  <div className="text-xs text-muted-foreground text-center">
                    Bằng cách đặt hàng, bạn đồng ý với{" "}
                    <Link href="/terms" className="underline hover:text-foreground">
                      Điều khoản dịch vụ
                    </Link>{" "}
                    và{" "}
                    <Link href="/privacy" className="underline hover:text-foreground">
                      Chính sách bảo mật
                    </Link>{" "}
                    của chúng tôi.
                  </div>
                </div>

                {/* Security Info */}
                <div className="pt-4 border-t">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <AlertCircle className="h-4 w-4" />
                    <span>Thông tin của bạn được bảo mật</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Chúng tôi sử dụng mã hóa SSL để bảo vệ thông tin thanh toán của bạn.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
