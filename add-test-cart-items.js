// Add some test items to cart for testing checkout
async function addTestItems() {
  const BASE_URL = 'http://localhost:3001';
  const TEST_USER_ID = 'user-001';
  
  const testItems = [
    { productId: 'laptop-001', quantity: 1 },
    { productId: 'phone-001', quantity: 2 }
  ];

  console.log('Adding test items to cart...\n');

  for (const item of testItems) {
    try {
      const response = await fetch(`${BASE_URL}/api/cart`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: TEST_USER_ID,
          productId: item.productId,
          quantity: item.quantity
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log(`✅ Added ${item.quantity}x ${data.cartItem.product.name}`);
      } else {
        console.log(`❌ Failed to add ${item.productId}:`, data.error);
      }
    } catch (error) {
      console.log(`❌ Error adding ${item.productId}:`, error.message);
    }
  }

  // Get final cart state
  try {
    const response = await fetch(`${BASE_URL}/api/cart?userId=${TEST_USER_ID}`);
    const cartData = await response.json();
    
    if (response.ok) {
      console.log(`\n📦 Cart now has ${cartData.totalItems} items worth ${cartData.totalAmount.toLocaleString()} VND`);
      console.log('\n🎯 Ready for checkout testing!');
      console.log('Next steps:');
      console.log('1. Open browser and go to http://localhost:3001/login');
      console.log('2. Login with: <EMAIL> / password123');
      console.log('3. Go to http://localhost:3001/checkout to test checkout page');
    }
  } catch (error) {
    console.log('❌ Error getting cart:', error.message);
  }
}

addTestItems();
