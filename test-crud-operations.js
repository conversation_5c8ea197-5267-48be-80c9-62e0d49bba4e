// Test CRUD operations and data persistence
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing CRUD operations and data persistence...\n');

const dbPath = path.join(process.cwd(), 'data', 'store.json');

// Helper functions
function loadDatabase() {
  return JSON.parse(fs.readFileSync(dbPath, 'utf-8'));
}

function saveDatabase(data) {
  fs.writeFileSync(dbPath, JSON.stringify(data, null, 2));
}

function generateId(prefix) {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

try {
  console.log('📖 Loading initial database...');
  let db = loadDatabase();
  const initialCounts = {
    categories: db.categories.length,
    users: db.users.length,
    products: db.products.length,
    orders: db.orders.length
  };
  console.log('✓ Initial data loaded');
  console.log(`   Categories: ${initialCounts.categories}`);
  console.log(`   Users: ${initialCounts.users}`);
  console.log(`   Products: ${initialCounts.products}`);
  console.log(`   Orders: ${initialCounts.orders}`);

  // Test CREATE operations
  console.log('\n➕ Testing CREATE operations...');
  
  // Create a new category
  const newCategory = {
    id: generateId('test-cat'),
    name: 'Test Category',
    slug: 'test-category',
    description: 'A test category for CRUD testing',
    image: 'https://example.com/test.jpg',
    status: 'active',
    created_at: new Date().toISOString()
  };
  db.categories.push(newCategory);
  console.log('✓ Created new category:', newCategory.name);

  // Create a new user
  const newUser = {
    id: generateId('test-user'),
    name: 'Test User',
    email: '<EMAIL>',
    password: 'hashedpassword123',
    phone: '0123456789',
    role: 'customer',
    status: 'active',
    created_at: new Date().toISOString()
  };
  db.users.push(newUser);
  console.log('✓ Created new user:', newUser.name);

  // Create a new product
  const newProduct = {
    id: generateId('test-prod'),
    name: 'Test Product',
    description: 'A test product for CRUD testing',
    price: 1000000,
    original_price: 1200000,
    image: 'https://example.com/product.jpg',
    images: JSON.stringify(['https://example.com/product1.jpg', 'https://example.com/product2.jpg']),
    category_id: newCategory.id,
    stock: 10,
    rating: 4.5,
    review_count: 5,
    sold_count: 2,
    featured: true,
    is_promotional: false,
    promotion_ends: null,
    created_at: new Date().toISOString()
  };
  db.products.push(newProduct);
  console.log('✓ Created new product:', newProduct.name);

  // Save database after CREATE operations
  saveDatabase(db);
  console.log('✓ Database saved after CREATE operations');

  // Test READ operations
  console.log('\n📖 Testing READ operations...');
  
  // Reload database to test persistence
  db = loadDatabase();
  
  // Find created category
  const foundCategory = db.categories.find(c => c.id === newCategory.id);
  if (foundCategory) {
    console.log('✓ Found created category:', foundCategory.name);
  } else {
    throw new Error('Created category not found!');
  }

  // Find created user
  const foundUser = db.users.find(u => u.id === newUser.id);
  if (foundUser) {
    console.log('✓ Found created user:', foundUser.name);
  } else {
    throw new Error('Created user not found!');
  }

  // Find created product
  const foundProduct = db.products.find(p => p.id === newProduct.id);
  if (foundProduct) {
    console.log('✓ Found created product:', foundProduct.name);
  } else {
    throw new Error('Created product not found!');
  }

  // Test filtering and searching
  const featuredProducts = db.products.filter(p => p.featured);
  console.log(`✓ Found ${featuredProducts.length} featured products`);

  const testCategoryProducts = db.products.filter(p => p.category_id === newCategory.id);
  console.log(`✓ Found ${testCategoryProducts.length} products in test category`);

  // Test UPDATE operations
  console.log('\n✏️ Testing UPDATE operations...');
  
  // Update category
  const categoryIndex = db.categories.findIndex(c => c.id === newCategory.id);
  if (categoryIndex !== -1) {
    db.categories[categoryIndex].name = 'Updated Test Category';
    db.categories[categoryIndex].description = 'Updated description';
    console.log('✓ Updated category name and description');
  }

  // Update user
  const userIndex = db.users.findIndex(u => u.id === newUser.id);
  if (userIndex !== -1) {
    db.users[userIndex].name = 'Updated Test User';
    db.users[userIndex].phone = '0987654321';
    console.log('✓ Updated user name and phone');
  }

  // Update product
  const productIndex = db.products.findIndex(p => p.id === newProduct.id);
  if (productIndex !== -1) {
    db.products[productIndex].name = 'Updated Test Product';
    db.products[productIndex].price = 900000;
    db.products[productIndex].stock = 15;
    console.log('✓ Updated product name, price, and stock');
  }

  // Save database after UPDATE operations
  saveDatabase(db);
  console.log('✓ Database saved after UPDATE operations');

  // Verify updates persisted
  db = loadDatabase();
  const updatedCategory = db.categories.find(c => c.id === newCategory.id);
  const updatedUser = db.users.find(u => u.id === newUser.id);
  const updatedProduct = db.products.find(p => p.id === newProduct.id);

  if (updatedCategory.name === 'Updated Test Category') {
    console.log('✓ Category update persisted');
  } else {
    throw new Error('Category update did not persist!');
  }

  if (updatedUser.name === 'Updated Test User') {
    console.log('✓ User update persisted');
  } else {
    throw new Error('User update did not persist!');
  }

  if (updatedProduct.name === 'Updated Test Product' && updatedProduct.price === 900000) {
    console.log('✓ Product update persisted');
  } else {
    throw new Error('Product update did not persist!');
  }

  // Test DELETE operations
  console.log('\n🗑️ Testing DELETE operations...');
  
  // Delete created items
  db.categories = db.categories.filter(c => c.id !== newCategory.id);
  console.log('✓ Deleted test category');

  db.users = db.users.filter(u => u.id !== newUser.id);
  console.log('✓ Deleted test user');

  db.products = db.products.filter(p => p.id !== newProduct.id);
  console.log('✓ Deleted test product');

  // Save database after DELETE operations
  saveDatabase(db);
  console.log('✓ Database saved after DELETE operations');

  // Verify deletions persisted
  db = loadDatabase();
  const deletedCategory = db.categories.find(c => c.id === newCategory.id);
  const deletedUser = db.users.find(u => u.id === newUser.id);
  const deletedProduct = db.products.find(p => p.id === newProduct.id);

  if (!deletedCategory && !deletedUser && !deletedProduct) {
    console.log('✓ All deletions persisted');
  } else {
    throw new Error('Some deletions did not persist!');
  }

  // Verify data integrity
  console.log('\n🔍 Verifying data integrity...');
  const finalCounts = {
    categories: db.categories.length,
    users: db.users.length,
    products: db.products.length,
    orders: db.orders.length
  };

  if (finalCounts.categories === initialCounts.categories &&
      finalCounts.users === initialCounts.users &&
      finalCounts.products === initialCounts.products &&
      finalCounts.orders === initialCounts.orders) {
    console.log('✓ Data integrity maintained - counts match initial state');
  } else {
    console.log('⚠️ Data counts changed:');
    console.log(`   Categories: ${initialCounts.categories} → ${finalCounts.categories}`);
    console.log(`   Users: ${initialCounts.users} → ${finalCounts.users}`);
    console.log(`   Products: ${initialCounts.products} → ${finalCounts.products}`);
    console.log(`   Orders: ${initialCounts.orders} → ${finalCounts.orders}`);
  }

  // Test Vietnamese data characteristics
  console.log('\n🇻🇳 Verifying Vietnamese e-commerce data characteristics...');
  
  const vietnameseCategories = db.categories.filter(c => 
    c.name.includes('Điện thoại') || 
    c.name.includes('Laptop') || 
    c.name.includes('Máy tính')
  );
  console.log(`✓ Found ${vietnameseCategories.length} Vietnamese category names`);

  const vietnameseUsers = db.users.filter(u => 
    u.name.includes('Nguyễn') || 
    u.name.includes('Trần') || 
    u.name.includes('Lê')
  );
  console.log(`✓ Found ${vietnameseUsers.length} Vietnamese user names`);

  const vndProducts = db.products.filter(p => p.price >= 100000); // Typical VND amounts
  console.log(`✓ Found ${vndProducts.length} products with VND pricing`);

  const vietnameseAddresses = db.user_addresses.filter(a => 
    a.city.includes('Hồ Chí Minh') || 
    a.city.includes('Hà Nội') || 
    a.district.includes('Quận')
  );
  console.log(`✓ Found ${vietnameseAddresses.length} Vietnamese addresses`);

  console.log('\n🎉 All CRUD operations and data persistence tests completed successfully!');
  console.log('✅ Database is fully functional with persistent storage');
  console.log('✅ Vietnamese e-commerce data characteristics are maintained');

} catch (error) {
  console.error('❌ CRUD test failed:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}
