"use client"

import { useState, useEffect } from "react"
import { toast } from "@/components/ui/use-toast"
import type { Product } from "@/lib/types"

interface CartItem {
  id: string
  productId: string
  product: Product
  quantity: number
}

interface CartState {
  items: CartItem[]
  totalItems: number
  totalAmount: number
  isLoading: boolean
}

export function useCart() {
  const [cart, setCart] = useState<CartState>({
    items: [],
    totalItems: 0,
    totalAmount: 0,
    isLoading: false,
  })

  // Get user from localStorage
  const getUser = () => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem("user")
      return userData ? JSON.parse(userData) : null
    }
    return null
  }

  // Fetch cart items
  const fetchCart = async () => {
    const user = getUser()
    if (!user) return

    setCart(prev => ({ ...prev, isLoading: true }))
    
    try {
      const response = await fetch(`/api/cart?userId=${user.id}`)
      if (response.ok) {
        const data = await response.json()
        setCart({
          items: data.items || [],
          totalItems: data.totalItems || 0,
          totalAmount: data.totalAmount || 0,
          isLoading: false,
        })
      }
    } catch (error) {
      console.error("Error fetching cart:", error)
      setCart(prev => ({ ...prev, isLoading: false }))
    }
  }

  // Add item to cart
  const addToCart = async (product: Product, quantity: number = 1) => {
    const user = getUser()
    if (!user) {
      toast({
        title: "Yêu cầu đăng nhập",
        description: "Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng.",
        variant: "destructive",
      })
      return false
    }

    if (product.stock < quantity) {
      toast({
        title: "Không đủ hàng",
        description: `Chỉ còn ${product.stock} sản phẩm trong kho.`,
        variant: "destructive",
      })
      return false
    }

    try {
      const response = await fetch("/api/cart", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: user.id,
          productId: product.id,
          quantity,
        }),
      })

      if (response.ok) {
        await fetchCart() // Refresh cart
        toast({
          title: "Đã thêm vào giỏ hàng",
          description: `${product.name} đã được thêm vào giỏ hàng của bạn.`,
        })
        return true
      } else {
        const error = await response.json()
        toast({
          title: "Lỗi",
          description: error.error || "Không thể thêm sản phẩm vào giỏ hàng.",
          variant: "destructive",
        })
        return false
      }
    } catch (error) {
      console.error("Error adding to cart:", error)
      toast({
        title: "Lỗi",
        description: "Không thể thêm sản phẩm vào giỏ hàng.",
        variant: "destructive",
      })
      return false
    }
  }

  // Remove item from cart
  const removeFromCart = async (cartItemId: string) => {
    const user = getUser()
    if (!user) return false

    try {
      const response = await fetch(`/api/cart/${cartItemId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        await fetchCart() // Refresh cart
        toast({
          title: "Đã xóa khỏi giỏ hàng",
          description: "Sản phẩm đã được xóa khỏi giỏ hàng của bạn.",
        })
        return true
      }
    } catch (error) {
      console.error("Error removing from cart:", error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa sản phẩm khỏi giỏ hàng.",
        variant: "destructive",
      })
    }
    return false
  }

  // Update item quantity
  const updateQuantity = async (cartItemId: string, quantity: number) => {
    const user = getUser()
    if (!user) return false

    try {
      const response = await fetch(`/api/cart/${cartItemId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ quantity }),
      })

      if (response.ok) {
        await fetchCart() // Refresh cart
        return true
      }
    } catch (error) {
      console.error("Error updating quantity:", error)
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật số lượng sản phẩm.",
        variant: "destructive",
      })
    }
    return false
  }

  // Clear cart
  const clearCart = async () => {
    const user = getUser()
    if (!user) return false

    try {
      const response = await fetch(`/api/cart/clear?userId=${user.id}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setCart({
          items: [],
          totalItems: 0,
          totalAmount: 0,
          isLoading: false,
        })
        toast({
          title: "Đã xóa giỏ hàng",
          description: "Tất cả sản phẩm đã được xóa khỏi giỏ hàng.",
        })
        return true
      }
    } catch (error) {
      console.error("Error clearing cart:", error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa giỏ hàng.",
        variant: "destructive",
      })
    }
    return false
  }

  // Load cart on mount
  useEffect(() => {
    fetchCart()
  }, [])

  return {
    cart,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    refreshCart: fetchCart,
  }
}
