"use client"

import { useState } from "react"
import Link from "next/link"
import { Heart, ShoppingCart, Star, Eye, Share2, BarChart3, Plus } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"
import type { Product } from "@/lib/types"

interface ProductCardProps {
  product: Product
  onAddToCart?: (product: Product, quantity?: number) => void
  onToggleWishlist?: (product: Product, isWishlisted: boolean) => void
  onShare?: (product: Product) => void
  onCompare?: (product: Product) => void
}

export function ProductCard({
  product,
  onAddToCart,
  onToggleWishlist,
  onShare,
  onCompare
}: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false)

  const handleAddToCart = async () => {
    if (onAddToCart) {
      onAddToCart(product, 1)
    } else {
      // Fallback to toast notification
      toast({
        title: "Đã thêm vào giỏ hàng",
        description: `${product.name} đã được thêm vào giỏ hàng của bạn.`,
      })
    }
  }

  const handleQuickAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    handleAddToCart()
  }

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const newWishlistState = !isWishlisted
    setIsWishlisted(newWishlistState)

    if (onToggleWishlist) {
      onToggleWishlist(product, newWishlistState)
    } else {
      // Fallback to toast notification
      toast({
        title: newWishlistState ? "Đã thêm vào danh sách yêu thích" : "Đã xóa khỏi danh sách yêu thích",
        description: newWishlistState
          ? `${product.name} đã được thêm vào danh sách yêu thích của bạn.`
          : `${product.name} đã được xóa khỏi danh sách yêu thích của bạn.`,
      })
    }
  }

  const handleShare = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (onShare) {
      onShare(product)
    } else {
      // Fallback share functionality
      const url = `${window.location.origin}/product/${product.id}`
      if (navigator.share) {
        try {
          await navigator.share({
            title: product.name,
            text: product.description,
            url: url,
          })
        } catch (error) {
          console.error('Error sharing:', error)
        }
      } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(url)
        toast({
          title: "Đã sao chép",
          description: "Đường dẫn sản phẩm đã được sao chép vào clipboard",
        })
      }
    }
  }

  const handleCompare = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (onCompare) {
      onCompare(product)
    } else {
      toast({
        title: "Đã thêm vào so sánh",
        description: `${product.name} đã được thêm vào danh sách so sánh.`,
      })
    }
  }

  const discountPercentage = product.originalPrice
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  // Determine if this is a special promotion product
  const isSpecialPromo = String(product.id).startsWith("promo-")

  return (
    <TooltipProvider>
      <Card className="overflow-hidden group hover:shadow-lg transition-all duration-300">
        <div className="relative">
          <Link href={`/product/${product.id}`}>
            <img
              src={product.image || "/placeholder.svg?height=300&width=300"}
              alt={product.name}
              width={300}
              height={300}
              className="h-[200px] w-full object-cover transition-transform group-hover:scale-105"
            />
          </Link>

          {/* Discount Badge */}
          {product.originalPrice && (
            <Badge
              className={`absolute top-2 left-2 ${isSpecialPromo ? "bg-red-600 hover:bg-red-700" : "bg-red-500 hover:bg-red-600"}`}
            >
              -{discountPercentage}%
            </Badge>
          )}

          {/* Special Promo Badge */}
          {isSpecialPromo && (
            <Badge className="absolute top-10 left-2 bg-yellow-500 hover:bg-yellow-600">
              Hot Deal
            </Badge>
          )}

          {/* Action Buttons Overlay */}
          <div className="absolute top-2 right-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            {/* Wishlist Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className={`rounded-full backdrop-blur-sm ${isWishlisted ? "bg-red-100 border-red-300" : "bg-white/90"}`}
                  onClick={handleToggleWishlist}
                >
                  <Heart className={`h-4 w-4 ${isWishlisted ? "fill-red-500 text-red-500" : ""}`} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isWishlisted ? "Xóa khỏi yêu thích" : "Thêm vào yêu thích"}</p>
              </TooltipContent>
            </Tooltip>

            {/* Quick View Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Dialog open={isQuickViewOpen} onOpenChange={setIsQuickViewOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className="rounded-full backdrop-blur-sm bg-white/90"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                </Dialog>
              </TooltipTrigger>
              <TooltipContent>
                <p>Xem nhanh</p>
              </TooltipContent>
            </Tooltip>

            {/* Share Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-full backdrop-blur-sm bg-white/90"
                  onClick={handleShare}
                >
                  <Share2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Chia sẻ</p>
              </TooltipContent>
            </Tooltip>

            {/* Compare Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-full backdrop-blur-sm bg-white/90"
                  onClick={handleCompare}
                >
                  <BarChart3 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>So sánh</p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Quick Add to Cart Button */}
          <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  className="rounded-full"
                  onClick={handleQuickAddToCart}
                  disabled={product.stock === 0}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Thêm nhanh vào giỏ</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
        <CardContent className="p-4">
          <Link href={`/product/${product.id}`} className="hover:underline">
            <h3 className="font-semibold line-clamp-2 min-h-[48px] mb-2">{product.name}</h3>
          </Link>

          {/* Rating and Reviews */}
          <div className="flex items-center gap-1 mb-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${i < product.rating ? "fill-primary text-primary" : "fill-muted text-muted"}`}
              />
            ))}
            <span className="text-xs text-muted-foreground ml-1">({product.reviewCount})</span>
            {product.soldCount > 0 && (
              <>
                <span className="text-xs text-muted-foreground mx-1">•</span>
                <span className="text-xs text-muted-foreground">Đã bán {product.soldCount}</span>
              </>
            )}
          </div>

          {/* Price Section */}
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <span className="font-bold text-lg text-primary">{formatCurrency(product.price)}</span>
              {product.originalPrice && (
                <span className="text-sm text-muted-foreground line-through">
                  {formatCurrency(product.originalPrice)}
                </span>
              )}
            </div>

            {/* Stock Status */}
            {product.stock <= 5 && product.stock > 0 && (
              <p className="text-xs text-orange-500 font-medium">⚠️ Chỉ còn {product.stock} sản phẩm</p>
            )}
            {product.stock === 0 && (
              <p className="text-xs text-red-500 font-medium">❌ Hết hàng</p>
            )}
            {product.stock > 5 && (
              <p className="text-xs text-green-600 font-medium">✅ Còn hàng</p>
            )}
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 space-y-2">
          {/* Main Add to Cart Button */}
          <Button
            className="w-full"
            onClick={handleAddToCart}
            disabled={product.stock === 0}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            {product.stock === 0 ? "Hết hàng" : "Thêm vào giỏ"}
          </Button>

          {/* Secondary Actions */}
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              asChild
            >
              <Link href={`/product/${product.id}`}>
                <Eye className="mr-1 h-3 w-3" />
                Xem chi tiết
              </Link>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleWishlist}
              className={isWishlisted ? "text-red-500 border-red-300" : ""}
            >
              <Heart className={`h-3 w-3 ${isWishlisted ? "fill-current" : ""}`} />
            </Button>
          </div>
        </CardFooter>

        {/* Quick View Modal */}
        <Dialog open={isQuickViewOpen} onOpenChange={setIsQuickViewOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{product.name}</DialogTitle>
            </DialogHeader>
            <div className="grid gap-6 md:grid-cols-2">
              {/* Product Image */}
              <div className="aspect-square overflow-hidden rounded-lg">
                <img
                  src={product.image || "/placeholder.svg"}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Product Details */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                  <p className="text-muted-foreground">{product.description}</p>
                </div>

                {/* Rating */}
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${i < product.rating ? "fill-primary text-primary" : "fill-muted text-muted"}`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {product.rating}/5 ({product.reviewCount} đánh giá)
                  </span>
                </div>

                {/* Price */}
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-primary">{formatCurrency(product.price)}</span>
                    {product.originalPrice && (
                      <span className="text-lg text-muted-foreground line-through">
                        {formatCurrency(product.originalPrice)}
                      </span>
                    )}
                  </div>
                  {discountPercentage > 0 && (
                    <Badge className="bg-red-500">Tiết kiệm {discountPercentage}%</Badge>
                  )}
                </div>

                {/* Stock Status */}
                <div className="text-sm">
                  {product.stock > 0 ? (
                    <span className="text-green-600">✅ Còn {product.stock} sản phẩm</span>
                  ) : (
                    <span className="text-red-500">❌ Hết hàng</span>
                  )}
                </div>

                {/* Actions */}
                <div className="space-y-3">
                  <Button
                    className="w-full"
                    onClick={handleAddToCart}
                    disabled={product.stock === 0}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Thêm vào giỏ hàng
                  </Button>

                  <div className="flex gap-2">
                    <Button variant="outline" className="flex-1" asChild>
                      <Link href={`/product/${product.id}`}>
                        Xem chi tiết đầy đủ
                      </Link>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleToggleWishlist}
                      className={isWishlisted ? "text-red-500 border-red-300" : ""}
                    >
                      <Heart className={`h-4 w-4 ${isWishlisted ? "fill-current" : ""}`} />
                    </Button>
                    <Button variant="outline" onClick={handleShare}>
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </Card>
    </TooltipProvider>
  )
}
