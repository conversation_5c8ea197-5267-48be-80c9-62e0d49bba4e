"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ShoppingBag, User, LogOut, Package, Settings } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface Category {
  id: string
  name: string
  slug: string
}

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice: number
  image: string
  images: string[]
  category: Category
  stock: number
  rating: number
  reviewCount: number
  soldCount: number
  featured?: boolean
  promotional?: boolean
  createdAt: string
}

export default function HomePage() {
  const [user, setUser] = useState<User | null>(null)
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [newArrivals, setNewArrivals] = useState<Product[]>([])
  const [promotionalProducts, setPromotionalProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Lấy thông tin user từ localStorage
    const userStr = localStorage.getItem("user")
    if (userStr) {
      setUser(JSON.parse(userStr))
      // Nếu đã đăng nhập, chuyển hướng đến trang chủ cho người dùng đã đăng nhập
      router.push("/dashboard")
    }

    // Lấy dữ liệu sản phẩm
    const fetchProducts = async () => {
      try {
        setLoading(true)
        setError(null)

        // Lấy sản phẩm nổi bật
        const featuredResponse = await fetch('/api/products?featured=true&limit=4')
        if (!featuredResponse.ok) {
          throw new Error('Failed to fetch featured products')
        }
        const featuredData = await featuredResponse.json()
        setFeaturedProducts(Array.isArray(featuredData) ? featuredData : [])

        // Lấy sản phẩm mới
        const newArrivalsResponse = await fetch('/api/products?sort=newest&limit=4')
        if (!newArrivalsResponse.ok) {
          throw new Error('Failed to fetch new arrivals')
        }
        const newArrivalsData = await newArrivalsResponse.json()
        setNewArrivals(Array.isArray(newArrivalsData) ? newArrivalsData : [])

        // Lấy sản phẩm khuyến mãi
        const promotionalResponse = await fetch('/api/products/promotional?limit=4')
        if (!promotionalResponse.ok) {
          throw new Error('Failed to fetch promotional products')
        }
        const promotionalData = await promotionalResponse.json()
        setPromotionalProducts(Array.isArray(promotionalData) ? promotionalData : [])

      } catch (error) {
        console.error('Error fetching products:', error)
        setError('Không thể tải sản phẩm. Vui lòng thử lại sau.')
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [router])

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between px-8 md:px-12 lg:px-20">
          <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
            <ShoppingBag className="h-6 w-6" />
            <span>TechStore</span>
          </Link>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Button variant="ghost" asChild>
                <Link href="/login">Đăng nhập</Link>
              </Button>
              <Button asChild>
                <Link href="/register">Đăng ký</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1">
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-8 md:px-12 lg:px-20">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                    Chào mừng đến với TechStore
                  </h1>
                  <p className="max-w-[600px] text-muted-foreground md:text-xl">
                    Khám phá thế giới công nghệ với những sản phẩm chất lượng cao và giá cả phải chăng.
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Button asChild>
                    <Link href="/products">Xem sản phẩm</Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/register">Tạo tài khoản</Link>
                    </Button>
                </div>
              </div>
              <div className="flex items-center justify-center">
                      <img
                  src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=1472&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  alt="Hero"
                  className="aspect-video overflow-hidden rounded-xl object-cover object-center"
                  width="600"
                  height="400"
                      />
              </div>
            </div>
          </div>
        </section>
        
        <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
          <div className="container px-8 md:px-12 lg:px-20">
            <h2 className="text-3xl font-bold tracking-tighter mb-8">Sản phẩm nổi bật</h2>
            {loading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {[...Array(4)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="aspect-square bg-gray-200 rounded-lg"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                      <div className="h-10 bg-gray-200 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500 mb-4">{error}</p>
                <Button onClick={() => window.location.reload()}>Thử lại</Button>
              </div>
            ) : featuredProducts.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {featuredProducts.map((product) => (
                  <Card key={product.id}>
                    <CardHeader>
                      <img
                        src={product.image || '/placeholder.svg'}
                        alt={product.name}
                        className="aspect-square object-cover rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder.svg';
                        }}
                      />
                      <CardTitle className="line-clamp-2">{product.name}</CardTitle>
                      <CardDescription className="line-clamp-2">{product.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2 mb-2">
                        <p className="text-2xl font-bold text-primary">{product.price.toLocaleString('vi-VN')}đ</p>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <p className="text-sm text-muted-foreground line-through">
                            {product.originalPrice.toLocaleString('vi-VN')}đ
                          </p>
                        )}
                      </div>
                      <Button className="w-full mt-4" asChild>
                        <Link href={`/products/${product.id}`}>Xem chi tiết</Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Không có sản phẩm nổi bật nào.</p>
              </div>
            )}
          </div>
        </section>
        
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-8 md:px-12 lg:px-20">
            <h2 className="text-3xl font-bold tracking-tighter mb-8">Sản phẩm mới</h2>
            {loading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {[...Array(4)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="aspect-square bg-gray-200 rounded-lg"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                      <div className="h-10 bg-gray-200 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : newArrivals.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {newArrivals.map((product) => (
                  <Card key={product.id}>
                    <CardHeader>
                      <img
                        src={product.image || '/placeholder.svg'}
                        alt={product.name}
                        className="aspect-square object-cover rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder.svg';
                        }}
                      />
                      <CardTitle className="line-clamp-2">{product.name}</CardTitle>
                      <CardDescription className="line-clamp-2">{product.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2 mb-2">
                        <p className="text-2xl font-bold text-primary">{product.price.toLocaleString('vi-VN')}đ</p>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <p className="text-sm text-muted-foreground line-through">
                            {product.originalPrice.toLocaleString('vi-VN')}đ
                          </p>
                        )}
                      </div>
                      <Button className="w-full mt-4" asChild>
                        <Link href={`/products/${product.id}`}>Xem chi tiết</Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Không có sản phẩm mới nào.</p>
              </div>
            )}
          </div>
        </section>

        <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
          <div className="container px-8 md:px-12 lg:px-20">
            <h2 className="text-3xl font-bold tracking-tighter mb-8">Khuyến mãi hấp dẫn</h2>
            {loading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {[...Array(4)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="aspect-square bg-gray-200 rounded-lg"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                      <div className="h-10 bg-gray-200 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : promotionalProducts.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {promotionalProducts.map((product) => (
                  <Card key={product.id} className="relative">
                    <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-semibold z-10">
                      KHUYẾN MÃI
                    </div>
                    <CardHeader>
                      <img
                        src={product.image || '/placeholder.svg'}
                        alt={product.name}
                        className="aspect-square object-cover rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder.svg';
                        }}
                      />
                      <CardTitle className="line-clamp-2">{product.name}</CardTitle>
                      <CardDescription className="line-clamp-2">{product.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2 mb-2">
                        <p className="text-2xl font-bold text-red-600">{product.price.toLocaleString('vi-VN')}đ</p>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <p className="text-sm text-muted-foreground line-through">
                            {product.originalPrice.toLocaleString('vi-VN')}đ
                          </p>
                        )}
                      </div>
                      {product.originalPrice && product.originalPrice > product.price && (
                        <p className="text-sm text-green-600 font-semibold mb-2">
                          Tiết kiệm {((product.originalPrice - product.price) / product.originalPrice * 100).toFixed(0)}%
                        </p>
                      )}
                      <Button className="w-full mt-4" asChild>
                        <Link href={`/products/${product.id}`}>Xem chi tiết</Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Không có sản phẩm khuyến mãi nào.</p>
              </div>
            )}
          </div>
        </section>
      </main>

      <footer className="border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row px-8 md:px-12 lg:px-20">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            Built by{" "}
            <a
              href="#"
              target="_blank"
              rel="noreferrer"
              className="font-medium underline underline-offset-4"
            >
              TechStore
            </a>
            . All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
