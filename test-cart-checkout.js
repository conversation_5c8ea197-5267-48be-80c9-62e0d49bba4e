// Test script to add items to cart and test checkout functionality
// Using Node.js built-in fetch (Node 18+)

const BASE_URL = 'http://localhost:3001';
const TEST_USER_ID = 'user-001';
const TEST_PRODUCT_ID = 'laptop-001';

async function testCartAndCheckout() {
  console.log('🧪 Testing Cart and Checkout Functionality\n');

  try {
    // Test 1: Add item to cart
    console.log('1. Testing Add to Cart...');
    const addToCartResponse = await fetch(`${BASE_URL}/api/cart`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: TEST_USER_ID,
        productId: TEST_PRODUCT_ID,
        quantity: 2
      })
    });

    const addToCartData = await addToCartResponse.json();
    
    if (addToCartResponse.ok) {
      console.log('✅ Successfully added to cart');
      console.log(`   Product: ${addToCartData.cartItem.product.name}`);
      console.log(`   Quantity: ${addToCartData.cartItem.quantity}`);
      console.log(`   Total items in cart: ${addToCartData.totalItems}`);
    } else {
      console.log('❌ Failed to add to cart:', addToCartData.error);
      return;
    }

    // Test 2: Get cart contents
    console.log('\n2. Testing Get Cart...');
    const getCartResponse = await fetch(`${BASE_URL}/api/cart?userId=${TEST_USER_ID}`);
    const cartData = await getCartResponse.json();

    if (getCartResponse.ok) {
      console.log('✅ Successfully retrieved cart');
      console.log(`   Items in cart: ${cartData.items.length}`);
      console.log(`   Total items: ${cartData.totalItems}`);
      console.log(`   Total amount: ${cartData.totalAmount.toLocaleString()} VND`);
      
      cartData.items.forEach((item, index) => {
        console.log(`   Item ${index + 1}: ${item.product.name} x${item.quantity} = ${(item.product.price * item.quantity).toLocaleString()} VND`);
      });
    } else {
      console.log('❌ Failed to get cart:', cartData.error);
      return;
    }

    // Test 3: Test order creation (simulating checkout)
    console.log('\n3. Testing Order Creation...');
    const orderData = {
      id: `order-test-${Date.now()}`,
      userId: TEST_USER_ID,
      items: cartData.items.map(item => ({
        id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        productId: item.product.id,
        product: {
          id: item.product.id,
          name: item.product.name,
          price: item.product.price,
          image: item.product.image
        },
        quantity: item.quantity,
        price: item.product.price
      })),
      totalAmount: cartData.totalAmount + 30000, // Add shipping
      status: 'pending',
      paymentMethod: 'cod',
      shippingAddress: {
        fullName: 'Nguyễn Văn A',
        phone: '0987654321',
        address: '123 Đường ABC',
        city: 'Hồ Chí Minh',
        district: 'Quận 1',
        ward: 'Phường Bến Nghé'
      }
    };

    const createOrderResponse = await fetch(`${BASE_URL}/api/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData)
    });

    const orderResult = await createOrderResponse.json();

    if (createOrderResponse.ok) {
      console.log('✅ Successfully created order');
      console.log(`   Order ID: ${orderResult.id}`);
      console.log(`   Status: ${orderResult.status}`);
      console.log(`   Total: ${orderResult.totalAmount.toLocaleString()} VND`);
      console.log(`   Items: ${orderResult.items.length}`);
    } else {
      console.log('❌ Failed to create order:', orderResult.error);
      return;
    }

    // Test 4: Clear cart (simulating post-checkout)
    console.log('\n4. Testing Cart Clear...');
    const clearCartResponse = await fetch(`${BASE_URL}/api/cart/clear?userId=${TEST_USER_ID}`, {
      method: 'DELETE'
    });

    const clearResult = await clearCartResponse.json();

    if (clearCartResponse.ok) {
      console.log('✅ Successfully cleared cart');
      console.log(`   Total items: ${clearResult.totalItems}`);
    } else {
      console.log('❌ Failed to clear cart:', clearResult.error);
    }

    // Test 5: Verify cart is empty
    console.log('\n5. Verifying cart is empty...');
    const verifyCartResponse = await fetch(`${BASE_URL}/api/cart?userId=${TEST_USER_ID}`);
    const verifyCartData = await verifyCartResponse.json();

    if (verifyCartResponse.ok) {
      console.log('✅ Cart verification complete');
      console.log(`   Items in cart: ${verifyCartData.items.length}`);
      console.log(`   Total items: ${verifyCartData.totalItems}`);
    } else {
      console.log('❌ Failed to verify cart:', verifyCartData.error);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Open http://localhost:3001/login and login with:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    console.log('2. Go to a product page and test "Add to Cart"');
    console.log('3. Go to /cart and test the cart page');
    console.log('4. Click "Thanh toán" to test the checkout page');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testCartAndCheckout();
