[{"id": "trans-001", "orderId": "order-001", "userId": "user-001", "amount": 15000000, "status": "completed", "paymentMethod": "VNPay", "transactionFee": 45000, "createdAt": "2024-01-15T00:00:00.000Z", "completedAt": "2024-01-15T00:05:23.000Z"}, {"id": "trans-002", "orderId": "order-002", "userId": "user-002", "amount": 8500000, "status": "pending", "paymentMethod": "<PERSON><PERSON>", "transactionFee": 25500, "createdAt": "2024-01-16T00:00:00.000Z"}, {"id": "trans-003", "orderId": "order-003", "userId": "user-003", "amount": 12000000, "status": "failed", "paymentMethod": "ZaloPay", "transactionFee": 36000, "createdAt": "2024-01-17T00:00:00.000Z", "failedAt": "2024-01-17T00:02:15.000Z", "failureReason": "Insufficient funds"}, {"id": "trans-004", "orderId": "order-004", "userId": "user-001", "amount": 9500000, "status": "completed", "paymentMethod": "VNPay", "transactionFee": 28500, "createdAt": "2024-01-18T00:00:00.000Z", "completedAt": "2024-01-18T00:03:45.000Z"}, {"id": "trans-005", "orderId": "order-005", "userId": "user-002", "amount": ********, "status": "pending", "paymentMethod": "<PERSON><PERSON>", "transactionFee": 75000, "createdAt": "2024-01-19T00:00:00.000Z"}, {"id": "trans-006", "orderId": "order-006", "userId": "user-003", "amount": ********, "status": "completed", "paymentMethod": "Bank Transfer", "transactionFee": 0, "createdAt": "2024-02-01T08:30:00.000Z", "completedAt": "2024-02-01T09:15:30.000Z"}, {"id": "trans-007", "orderId": "order-007", "userId": "user-004", "amount": ********, "status": "refunded", "paymentMethod": "VNPay", "transactionFee": 55500, "refundAmount": ********, "createdAt": "2024-02-03T14:20:00.000Z", "completedAt": "2024-02-03T14:25:12.000Z", "refundedAt": "2024-02-05T10:30:00.000Z", "refundReason": "Product defect"}, {"id": "trans-008", "orderId": "order-008", "userId": "user-005", "amount": 7200000, "status": "completed", "paymentMethod": "Cash on Delivery", "transactionFee": 0, "createdAt": "2024-02-05T16:45:00.000Z", "completedAt": "2024-02-06T09:20:00.000Z"}, {"id": "trans-009", "orderId": "order-009", "userId": "user-001", "amount": ********, "status": "completed", "paymentMethod": "ZaloPay", "transactionFee": 128970, "createdAt": "2024-02-08T11:15:00.000Z", "completedAt": "2024-02-08T11:18:45.000Z"}, {"id": "trans-010", "orderId": "order-010", "userId": "user-006", "amount": ********, "status": "cancelled", "paymentMethod": "<PERSON><PERSON>", "transactionFee": 47970, "createdAt": "2024-02-10T13:30:00.000Z", "cancelledAt": "2024-02-10T14:00:00.000Z", "cancellationReason": "Customer request"}, {"id": "trans-011", "orderId": "order-011", "userId": "user-007", "amount": 29990000, "status": "completed", "paymentMethod": "VNPay", "transactionFee": 89970, "createdAt": "2024-02-12T09:45:00.000Z", "completedAt": "2024-02-12T09:50:15.000Z"}, {"id": "trans-012", "orderId": "order-012", "userId": "user-002", "amount": 5990000, "status": "failed", "paymentMethod": "ZaloPay", "transactionFee": 17970, "createdAt": "2024-02-15T20:30:00.000Z", "failedAt": "2024-02-15T20:32:30.000Z", "failureReason": "Payment gateway timeout"}, {"id": "trans-013", "orderId": "order-013", "userId": "user-008", "amount": ********, "status": "completed", "paymentMethod": "Bank Transfer", "transactionFee": 0, "createdAt": "2024-02-18T07:20:00.000Z", "completedAt": "2024-02-18T08:45:00.000Z"}, {"id": "trans-014", "orderId": "order-014", "userId": "user-003", "amount": 8990000, "status": "pending", "paymentMethod": "<PERSON><PERSON>", "transactionFee": 26970, "createdAt": "2024-02-20T15:10:00.000Z"}, {"id": "trans-015", "orderId": "order-015", "userId": "user-009", "amount": ********, "status": "completed", "paymentMethod": "VNPay", "transactionFee": 105000, "createdAt": "2024-02-22T12:00:00.000Z", "completedAt": "2024-02-22T12:04:20.000Z"}, {"id": "trans-016", "orderId": "order-016", "userId": "user-010", "amount": ********, "status": "refunded", "paymentMethod": "ZaloPay", "transactionFee": 67500, "refundAmount": ********, "createdAt": "2024-02-25T10:30:00.000Z", "completedAt": "2024-02-25T10:35:10.000Z", "refundedAt": "2024-02-28T14:20:00.000Z", "refundReason": "Partial return - 1 item damaged"}, {"id": "trans-017", "orderId": "order-017", "userId": "user-004", "amount": 4500000, "status": "completed", "paymentMethod": "Cash on Delivery", "transactionFee": 0, "createdAt": "2024-03-01T16:20:00.000Z", "completedAt": "2024-03-02T11:30:00.000Z"}, {"id": "trans-018", "orderId": "order-018", "userId": "user-005", "amount": 19990000, "status": "failed", "paymentMethod": "VNPay", "transactionFee": 59970, "createdAt": "2024-03-05T14:45:00.000Z", "failedAt": "2024-03-05T14:47:25.000Z", "failureReason": "Card declined"}, {"id": "trans-019", "orderId": "order-019", "userId": "user-006", "amount": ********, "status": "completed", "paymentMethod": "<PERSON><PERSON>", "transactionFee": 49500, "createdAt": "2024-03-08T11:15:00.000Z", "completedAt": "2024-03-08T11:18:40.000Z"}, {"id": "trans-020", "orderId": "order-020", "userId": "user-001", "amount": ********, "status": "pending", "paymentMethod": "Bank Transfer", "transactionFee": 0, "createdAt": "2024-03-10T09:30:00.000Z"}, {"id": "trans-021", "orderId": "order-021", "userId": "user-007", "amount": ********, "status": "cancelled", "paymentMethod": "ZaloPay", "transactionFee": 41970, "createdAt": "2024-03-12T13:20:00.000Z", "cancelledAt": "2024-03-12T15:45:00.000Z", "cancellationReason": "Duplicate order"}, {"id": "trans-022", "orderId": "order-022", "userId": "user-008", "amount": 9800000, "status": "completed", "paymentMethod": "VNPay", "transactionFee": 29400, "createdAt": "2024-03-15T08:10:00.000Z", "completedAt": "2024-03-15T08:13:55.000Z"}, {"id": "trans-023", "orderId": "order-023", "userId": "user-009", "amount": 6750000, "status": "completed", "paymentMethod": "Cash on Delivery", "transactionFee": 0, "createdAt": "2024-03-18T17:30:00.000Z", "completedAt": "2024-03-19T10:15:00.000Z"}, {"id": "trans-024", "orderId": "order-024", "userId": "user-010", "amount": ********, "status": "completed", "paymentMethod": "Bank Transfer", "transactionFee": 0, "createdAt": "2024-03-20T14:00:00.000Z", "completedAt": "2024-03-20T15:30:00.000Z"}, {"id": "trans-025", "orderId": "order-025", "userId": "user-002", "amount": ********, "status": "pending", "paymentMethod": "<PERSON><PERSON>", "transactionFee": 33600, "createdAt": "2024-03-22T12:45:00.000Z"}, {"id": "trans-026", "orderId": "order-026", "userId": "user-007", "amount": ********, "status": "completed", "paymentMethod": "vnpay", "transactionFee": 56970, "createdAt": "2025-07-11T14:00:00.000Z", "completedAt": "2025-07-11T14:03:25.000Z"}, {"id": "trans-027", "orderId": "order-027", "userId": "user-008", "amount": ********, "status": "pending", "paymentMethod": "momo", "transactionFee": 38970, "createdAt": "2025-07-11T14:15:00.000Z"}, {"id": "trans-028", "orderId": "order-028", "userId": "user-009", "amount": ********, "status": "completed", "paymentMethod": "zalopay", "transactionFee": 74970, "createdAt": "2025-07-11T14:30:00.000Z", "completedAt": "2025-07-11T14:33:15.000Z"}, {"id": "trans-029", "orderId": "order-029", "userId": "user-010", "amount": 8500000, "status": "failed", "paymentMethod": "vnpay", "transactionFee": 25500, "createdAt": "2025-07-11T14:45:00.000Z", "failedAt": "2025-07-11T14:47:30.000Z", "failureReason": "Thẻ hết hạn"}, {"id": "trans-030", "orderId": "order-030", "userId": "user-011", "amount": ********, "status": "completed", "paymentMethod": "bank_transfer", "transactionFee": 0, "createdAt": "2025-07-11T15:00:00.000Z", "completedAt": "2025-07-11T16:30:00.000Z"}, {"id": "trans-031", "orderId": "order-031", "userId": "user-012", "amount": 6990000, "status": "completed", "paymentMethod": "cod", "transactionFee": 0, "createdAt": "2025-07-11T15:15:00.000Z", "completedAt": "2025-07-12T09:20:00.000Z"}, {"id": "trans-032", "orderId": "order-032", "userId": "user-013", "amount": ********, "status": "refunded", "paymentMethod": "vnpay", "transactionFee": 98970, "refundAmount": ********, "createdAt": "2025-07-11T15:30:00.000Z", "completedAt": "2025-07-11T15:33:45.000Z", "refundedAt": "2025-07-13T10:15:00.000Z", "refundReason": "<PERSON><PERSON><PERSON> ph<PERSON>m không đúng mô tả"}, {"id": "trans-033", "orderId": "order-033", "userId": "user-014", "amount": 9990000, "status": "cancelled", "paymentMethod": "momo", "transactionFee": 29970, "createdAt": "2025-07-11T15:45:00.000Z", "cancelledAt": "2025-07-11T16:00:00.000Z", "cancellationReason": "<PERSON><PERSON><PERSON><PERSON> hàng thay đổi ý định"}, {"id": "trans-034", "orderId": "order-034", "userId": "user-007", "amount": 21990000, "status": "completed", "paymentMethod": "zalopay", "transactionFee": 65970, "createdAt": "2025-07-11T16:00:00.000Z", "completedAt": "2025-07-11T16:02:50.000Z"}, {"id": "trans-035", "orderId": "order-035", "userId": "user-008", "amount": 4990000, "status": "pending", "paymentMethod": "vnpay", "transactionFee": 14970, "createdAt": "2025-07-11T16:15:00.000Z"}]