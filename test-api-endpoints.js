// Test API endpoints with the database
const { CategoryModel, UserModel, ProductModel, OrderModel } = require('./lib/database/models');

console.log('🧪 Testing API endpoints with database...\n');

try {
  // Test CategoryModel
  console.log('1. Testing CategoryModel...');
  const categories = CategoryModel.getAll();
  console.log(`✓ Found ${categories.length} categories`);
  if (categories.length > 0) {
    console.log(`   First category: ${categories[0].name}`);
  }

  // Test UserModel
  console.log('\n2. Testing UserModel...');
  const users = UserModel.getAll();
  console.log(`✓ Found ${users.length} users`);
  if (users.length > 0) {
    console.log(`   First user: ${users[0].name}`);
  }

  // Test ProductModel
  console.log('\n3. Testing ProductModel...');
  const products = ProductModel.getAll({ limit: 5 });
  console.log(`✓ Found ${products.length} products (limited to 5)`);
  if (products.length > 0) {
    console.log(`   First product: ${products[0].name} - ${products[0].price} VND`);
  }

  // Test featured products
  console.log('\n4. Testing featured products...');
  const featuredProducts = ProductModel.getFeatured(3);
  console.log(`✓ Found ${featuredProducts.length} featured products`);

  // Test promotional products
  console.log('\n5. Testing promotional products...');
  const promotionalProducts = ProductModel.getPromotional(3);
  console.log(`✓ Found ${promotionalProducts.length} promotional products`);

  // Test OrderModel
  console.log('\n6. Testing OrderModel...');
  const orders = OrderModel.getAll();
  console.log(`✓ Found ${orders.length} orders`);
  if (orders.length > 0) {
    console.log(`   First order: ${orders[0].id} - ${orders[0].total_amount} VND`);
    
    // Test order details
    const orderDetails = OrderModel.getOrderWithDetails(orders[0].id);
    if (orderDetails) {
      console.log(`   Order has ${orderDetails.items.length} items`);
    }
  }

  // Test user orders
  if (users.length > 0) {
    console.log('\n7. Testing user orders...');
    const userOrders = OrderModel.getByUserId(users[0].id);
    console.log(`✓ User ${users[0].name} has ${userOrders.length} orders`);
  }

  console.log('\n🎉 All API endpoint tests completed successfully!');

} catch (error) {
  console.error('❌ API endpoint test failed:', error.message);
  console.error('Stack:', error.stack);
}
