// Test database directly
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing database directly...\n');

try {
  // Read the database file
  const dbPath = path.join(process.cwd(), 'data', 'store.json');
  console.log('Reading database from:', dbPath);
  
  if (!fs.existsSync(dbPath)) {
    console.error('❌ Database file not found!');
    process.exit(1);
  }
  
  const dbData = JSON.parse(fs.readFileSync(dbPath, 'utf-8'));
  
  console.log('✓ Database loaded successfully');
  console.log('\n📊 Database Summary:');
  console.log(`   Categories: ${dbData.categories.length}`);
  console.log(`   Users: ${dbData.users.length}`);
  console.log(`   User Addresses: ${dbData.user_addresses.length}`);
  console.log(`   Products: ${dbData.products.length}`);
  console.log(`   Reviews: ${dbData.reviews.length}`);
  console.log(`   Orders: ${dbData.orders.length}`);
  console.log(`   Order Items: ${dbData.order_items.length}`);
  console.log(`   Order Shipping Addresses: ${dbData.order_shipping_addresses.length}`);
  console.log(`   Transactions: ${dbData.transactions.length}`);
  
  // Test some data samples
  if (dbData.categories.length > 0) {
    console.log('\n📱 Sample Category:');
    console.log(`   ${dbData.categories[0].name} (${dbData.categories[0].slug})`);
  }
  
  if (dbData.users.length > 0) {
    console.log('\n👤 Sample User:');
    console.log(`   ${dbData.users[0].name} (${dbData.users[0].email})`);
  }
  
  if (dbData.products.length > 0) {
    console.log('\n📦 Sample Product:');
    const product = dbData.products[0];
    console.log(`   ${product.name} - ${product.price.toLocaleString()} VND`);
    console.log(`   Category: ${product.category_id}`);
    console.log(`   Featured: ${product.featured ? 'Yes' : 'No'}`);
    console.log(`   Promotional: ${product.is_promotional ? 'Yes' : 'No'}`);
  }
  
  if (dbData.orders.length > 0) {
    console.log('\n🛒 Sample Order:');
    const order = dbData.orders[0];
    console.log(`   Order ${order.id} - ${order.total_amount.toLocaleString()} VND`);
    console.log(`   Status: ${order.status}`);
    console.log(`   User: ${order.user_id}`);
    
    // Find order items
    const orderItems = dbData.order_items.filter(item => item.order_id === order.id);
    console.log(`   Items: ${orderItems.length}`);
  }
  
  // Test filtering
  console.log('\n🔍 Testing Filters:');
  const featuredProducts = dbData.products.filter(p => p.featured);
  console.log(`   Featured products: ${featuredProducts.length}`);
  
  const promotionalProducts = dbData.products.filter(p => p.is_promotional);
  console.log(`   Promotional products: ${promotionalProducts.length}`);
  
  const activeCategories = dbData.categories.filter(c => c.status === 'active');
  console.log(`   Active categories: ${activeCategories.length}`);
  
  const pendingOrders = dbData.orders.filter(o => o.status === 'pending');
  console.log(`   Pending orders: ${pendingOrders.length}`);
  
  console.log('\n🎉 Database test completed successfully!');
  console.log('✅ All data has been migrated and is accessible');

} catch (error) {
  console.error('❌ Database test failed:', error.message);
  console.error('Stack:', error.stack);
}
