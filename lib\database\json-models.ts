import fs from 'fs';
import path from 'path';

// JSON-based models as fallback when SQLite is not available
const DATA_DIR = path.join(process.cwd(), 'data');

// Helper function to read JSON file
function readJsonFile<T>(filename: string): T[] {
  try {
    const filePath = path.join(DATA_DIR, filename);
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return [];
  }
}

// Helper function to write JSON file
function writeJsonFile<T>(filename: string, data: T[]): void {
  try {
    const filePath = path.join(DATA_DIR, filename);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error(`Error writing ${filename}:`, error);
  }
}

// Transform JSON data to database format
function transformUser(user: any) {
  return {
    id: user.id,
    name: user.name,
    email: user.email,
    password: user.password,
    phone: user.phone || '',
    role: user.role || 'customer',
    status: user.status || 'active',
    created_at: user.createdAt || new Date().toISOString()
  };
}

function transformProduct(product: any) {
  return {
    id: product.id,
    name: product.name,
    description: product.description,
    price: product.price,
    original_price: product.originalPrice,
    image: product.image,
    images: JSON.stringify(product.images || []),
    category_id: product.category?.id || product.category?.slug || 'unknown',
    stock: product.stock || 0,
    rating: product.rating || 0,
    review_count: product.reviewCount || 0,
    sold_count: product.soldCount || 0,
    featured: Boolean(product.featured),
    is_promotional: Boolean(product.isPromotional),
    promotion_ends: product.promotionEnds || null,
    created_at: product.createdAt || new Date().toISOString()
  };
}

function transformCategory(category: any) {
  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    description: category.description,
    image: category.image,
    status: category.status || 'active',
    created_at: category.createdAt || new Date().toISOString()
  };
}

function transformTransaction(transaction: any) {
  return {
    id: transaction.id,
    order_id: transaction.orderId,
    user_id: transaction.userId,
    amount: transaction.amount,
    status: transaction.status,
    payment_method: transaction.paymentMethod,
    transaction_fee: transaction.transactionFee || 0,
    refund_amount: transaction.refundAmount || null,
    failure_reason: transaction.failureReason || null,
    refund_reason: transaction.refundReason || null,
    cancellation_reason: transaction.cancellationReason || null,
    created_at: transaction.createdAt || new Date().toISOString(),
    completed_at: transaction.completedAt || null,
    failed_at: transaction.failedAt || null,
    refunded_at: transaction.refundedAt || null,
    cancelled_at: transaction.cancelledAt || null
  };
}

function transformReview(review: any) {
  return {
    id: review.id,
    product_id: review.productId,
    user_id: review.userId,
    rating: review.rating,
    comment: review.comment,
    status: review.status,
    helpful_votes: review.helpfulVotes || 0,
    unhelpful_votes: review.unhelpfulVotes || 0,
    flag_reason: review.flagReason || null,
    rejection_reason: review.rejectionReason || null,
    created_at: review.createdAt || new Date().toISOString(),
    approved_at: review.approvedAt || null,
    rejected_at: review.rejectedAt || null,
    flagged_at: review.flaggedAt || null
  };
}

// JSON-based User Model
export class JsonUserModel {
  static getAll() {
    const users = readJsonFile<any>('users.json');
    return users.map(transformUser);
  }

  static getById(id: string) {
    const users = readJsonFile<any>('users.json');
    const user = users.find((u: any) => u.id === id);
    return user ? transformUser(user) : null;
  }

  static getByEmail(email: string) {
    const users = readJsonFile<any>('users.json');
    const user = users.find((u: any) => u.email === email);
    return user ? transformUser(user) : null;
  }

  static create(data: any) {
    const users = readJsonFile<any>('users.json');
    const newUser = {
      id: data.id,
      name: data.name,
      email: data.email,
      password: data.password,
      phone: data.phone || '',
      role: data.role || 'customer',
      status: data.status || 'active',
      createdAt: new Date().toISOString()
    };
    users.push(newUser);
    writeJsonFile('users.json', users);
    return transformUser(newUser);
  }

  static update(id: string, data: any) {
    const users = readJsonFile<any>('users.json');
    const index = users.findIndex((u: any) => u.id === id);
    if (index === -1) return null;
    
    users[index] = { ...users[index], ...data };
    writeJsonFile('users.json', users);
    return transformUser(users[index]);
  }

  static delete(id: string) {
    const users = readJsonFile<any>('users.json');
    const index = users.findIndex((u: any) => u.id === id);
    if (index === -1) return false;
    
    users.splice(index, 1);
    writeJsonFile('users.json', users);
    return true;
  }
}

// JSON-based Product Model
export class JsonProductModel {
  static getAll(options: any = {}) {
    const products = readJsonFile<any>('products.json');
    const promotionalProducts = readJsonFile<any>('promotionalProducts.json');
    
    let allProducts = [
      ...products.map((p: any) => ({ ...p, isPromotional: false })),
      ...promotionalProducts.map((p: any) => ({ ...p, isPromotional: true }))
    ];

    // Apply filters
    if (options.featured !== undefined) {
      allProducts = allProducts.filter((p: any) => Boolean(p.featured) === options.featured);
    }
    
    if (options.isPromotional !== undefined) {
      allProducts = allProducts.filter((p: any) => Boolean(p.isPromotional) === options.isPromotional);
    }
    
    if (options.categoryId) {
      allProducts = allProducts.filter((p: any) => 
        p.category?.id === options.categoryId || p.category?.slug === options.categoryId
      );
    }

    // Apply sorting
    if (options.orderBy) {
      allProducts.sort((a: any, b: any) => {
        const aVal = a[options.orderBy] || 0;
        const bVal = b[options.orderBy] || 0;
        return options.orderDirection === 'DESC' ? bVal - aVal : aVal - bVal;
      });
    }

    // Apply limit
    if (options.limit) {
      allProducts = allProducts.slice(0, options.limit);
    }

    return allProducts.map(transformProduct);
  }

  static getById(id: string) {
    const products = readJsonFile<any>('products.json');
    const promotionalProducts = readJsonFile<any>('promotionalProducts.json');
    
    let product = products.find((p: any) => p.id === id);
    if (!product) {
      product = promotionalProducts.find((p: any) => p.id === id);
      if (product) {
        product.isPromotional = true;
      }
    }
    
    return product ? transformProduct(product) : null;
  }

  static getFeatured(limit?: number) {
    return this.getAll({ featured: true, limit });
  }

  static getPromotional(limit?: number) {
    return this.getAll({ isPromotional: true, limit });
  }

  static getByCategoryId(categoryId: string, limit?: number) {
    return this.getAll({ categoryId, limit });
  }

  static search(query: string, limit?: number) {
    const allProducts = this.getAll();
    const filtered = allProducts.filter((p: any) => 
      p.name.toLowerCase().includes(query.toLowerCase()) ||
      (p.description && p.description.toLowerCase().includes(query.toLowerCase()))
    );
    
    return limit ? filtered.slice(0, limit) : filtered;
  }

  static create(data: any) {
    // For simplicity, add to regular products
    const products = readJsonFile<any>('products.json');
    const newProduct = {
      id: data.id,
      name: data.name,
      description: data.description,
      price: data.price,
      originalPrice: data.original_price,
      image: data.image,
      images: data.images ? JSON.parse(data.images) : [],
      category: { id: data.category_id },
      stock: data.stock || 0,
      rating: data.rating || 0,
      reviewCount: data.review_count || 0,
      soldCount: data.sold_count || 0,
      featured: Boolean(data.featured),
      createdAt: new Date().toISOString()
    };
    
    products.push(newProduct);
    writeJsonFile('products.json', products);
    return transformProduct(newProduct);
  }

  static update(id: string, data: any) {
    // Try to update in regular products first
    const products = readJsonFile<any>('products.json');
    const index = products.findIndex((p: any) => p.id === id);
    
    if (index !== -1) {
      products[index] = { ...products[index], ...data };
      writeJsonFile('products.json', products);
      return transformProduct(products[index]);
    }
    
    // Try promotional products
    const promoProducts = readJsonFile<any>('promotionalProducts.json');
    const promoIndex = promoProducts.findIndex((p: any) => p.id === id);
    
    if (promoIndex !== -1) {
      promoProducts[promoIndex] = { ...promoProducts[promoIndex], ...data };
      writeJsonFile('promotionalProducts.json', promoProducts);
      return transformProduct(promoProducts[promoIndex]);
    }
    
    return null;
  }

  static delete(id: string) {
    // Try to delete from regular products first
    const products = readJsonFile<any>('products.json');
    const index = products.findIndex((p: any) => p.id === id);
    
    if (index !== -1) {
      products.splice(index, 1);
      writeJsonFile('products.json', products);
      return true;
    }
    
    // Try promotional products
    const promoProducts = readJsonFile<any>('promotionalProducts.json');
    const promoIndex = promoProducts.findIndex((p: any) => p.id === id);
    
    if (promoIndex !== -1) {
      promoProducts.splice(promoIndex, 1);
      writeJsonFile('promotionalProducts.json', promoProducts);
      return true;
    }
    
    return false;
  }
}

// JSON-based Category Model
export class JsonCategoryModel {
  static getAll() {
    const categories = readJsonFile<any>('categories.json');
    return categories.map(transformCategory);
  }

  static getById(id: string) {
    const categories = readJsonFile<any>('categories.json');
    const category = categories.find((c: any) => c.id === id);
    return category ? transformCategory(category) : null;
  }

  static getBySlug(slug: string) {
    const categories = readJsonFile<any>('categories.json');
    const category = categories.find((c: any) => c.slug === slug);
    return category ? transformCategory(category) : null;
  }

  static create(data: any) {
    const categories = readJsonFile<any>('categories.json');
    const newCategory = {
      id: data.id,
      name: data.name,
      slug: data.slug,
      description: data.description,
      image: data.image,
      status: data.status || 'active',
      createdAt: new Date().toISOString()
    };
    
    categories.push(newCategory);
    writeJsonFile('categories.json', categories);
    return transformCategory(newCategory);
  }

  static update(id: string, data: any) {
    const categories = readJsonFile<any>('categories.json');
    const index = categories.findIndex((c: any) => c.id === id);
    if (index === -1) return null;
    
    categories[index] = { ...categories[index], ...data };
    writeJsonFile('categories.json', categories);
    return transformCategory(categories[index]);
  }

  static delete(id: string) {
    const categories = readJsonFile<any>('categories.json');
    const index = categories.findIndex((c: any) => c.id === id);
    if (index === -1) return false;

    categories.splice(index, 1);
    writeJsonFile('categories.json', categories);
    return true;
  }
}

// JSON-based Transaction Model
export class JsonTransactionModel {
  static getAll() {
    const transactions = readJsonFile<any>('transactions.json');
    return transactions.map(transformTransaction);
  }

  static getById(id: string) {
    const transactions = readJsonFile<any>('transactions.json');
    const transaction = transactions.find((t: any) => t.id === id);
    return transaction ? transformTransaction(transaction) : null;
  }

  static getByOrderId(orderId: string) {
    const transactions = readJsonFile<any>('transactions.json');
    const transaction = transactions.find((t: any) => t.orderId === orderId);
    return transaction ? transformTransaction(transaction) : null;
  }

  static getByUserId(userId: string) {
    const transactions = readJsonFile<any>('transactions.json');
    const userTransactions = transactions.filter((t: any) => t.userId === userId);
    return userTransactions.map(transformTransaction);
  }

  static create(data: any) {
    const transactions = readJsonFile<any>('transactions.json');
    const newTransaction = {
      id: data.id,
      orderId: data.order_id,
      userId: data.user_id,
      amount: data.amount,
      status: data.status || 'pending',
      paymentMethod: data.payment_method,
      transactionFee: data.transaction_fee || 0,
      createdAt: new Date().toISOString()
    };

    transactions.push(newTransaction);
    writeJsonFile('transactions.json', transactions);
    return transformTransaction(newTransaction);
  }

  static update(id: string, data: any) {
    const transactions = readJsonFile<any>('transactions.json');
    const index = transactions.findIndex((t: any) => t.id === id);
    if (index === -1) return null;

    // Transform database field names to JSON field names
    const updateData: any = {};
    if (data.status) updateData.status = data.status;
    if (data.refund_amount) updateData.refundAmount = data.refund_amount;
    if (data.failure_reason) updateData.failureReason = data.failure_reason;
    if (data.refund_reason) updateData.refundReason = data.refund_reason;
    if (data.cancellation_reason) updateData.cancellationReason = data.cancellation_reason;

    // Add timestamps based on status
    if (data.status === 'completed' && !transactions[index].completedAt) {
      updateData.completedAt = new Date().toISOString();
    } else if (data.status === 'failed' && !transactions[index].failedAt) {
      updateData.failedAt = new Date().toISOString();
    } else if (data.status === 'refunded' && !transactions[index].refundedAt) {
      updateData.refundedAt = new Date().toISOString();
    } else if (data.status === 'cancelled' && !transactions[index].cancelledAt) {
      updateData.cancelledAt = new Date().toISOString();
    }

    transactions[index] = { ...transactions[index], ...updateData };
    writeJsonFile('transactions.json', transactions);
    return transformTransaction(transactions[index]);
  }

  static delete(id: string) {
    const transactions = readJsonFile<any>('transactions.json');
    const index = transactions.findIndex((t: any) => t.id === id);
    if (index === -1) return false;

    transactions.splice(index, 1);
    writeJsonFile('transactions.json', transactions);
    return true;
  }
}

// JSON-based Review Model
export class JsonReviewModel {
  static getAll() {
    const reviews = readJsonFile<any>('reviews.json');
    return reviews.map(transformReview);
  }

  static getById(id: string) {
    const reviews = readJsonFile<any>('reviews.json');
    const review = reviews.find((r: any) => r.id === id);
    return review ? transformReview(review) : null;
  }

  static getByProductId(productId: string) {
    const reviews = readJsonFile<any>('reviews.json');
    const productReviews = reviews.filter((r: any) => r.productId === productId);
    return productReviews.map(transformReview);
  }

  static getByUserId(userId: string) {
    const reviews = readJsonFile<any>('reviews.json');
    const userReviews = reviews.filter((r: any) => r.userId === userId);
    return userReviews.map(transformReview);
  }

  static getByStatus(status: string) {
    const reviews = readJsonFile<any>('reviews.json');
    const statusReviews = reviews.filter((r: any) => r.status === status);
    return statusReviews.map(transformReview);
  }

  static create(data: any) {
    const reviews = readJsonFile<any>('reviews.json');
    const newReview = {
      id: data.id,
      productId: data.product_id,
      userId: data.user_id,
      rating: data.rating,
      comment: data.comment,
      status: data.status || 'pending',
      helpfulVotes: data.helpful_votes || 0,
      unhelpfulVotes: data.unhelpful_votes || 0,
      createdAt: new Date().toISOString()
    };

    reviews.push(newReview);
    writeJsonFile('reviews.json', reviews);
    return transformReview(newReview);
  }

  static update(id: string, data: any) {
    const reviews = readJsonFile<any>('reviews.json');
    const index = reviews.findIndex((r: any) => r.id === id);
    if (index === -1) return null;

    // Transform database field names to JSON field names
    const updateData: any = {};
    if (data.status) updateData.status = data.status;
    if (data.helpful_votes !== undefined) updateData.helpfulVotes = data.helpful_votes;
    if (data.unhelpful_votes !== undefined) updateData.unhelpfulVotes = data.unhelpful_votes;
    if (data.flag_reason) updateData.flagReason = data.flag_reason;
    if (data.rejection_reason) updateData.rejectionReason = data.rejection_reason;

    // Add timestamps based on status
    if (data.status === 'approved' && !reviews[index].approvedAt) {
      updateData.approvedAt = new Date().toISOString();
    } else if (data.status === 'rejected' && !reviews[index].rejectedAt) {
      updateData.rejectedAt = new Date().toISOString();
      if (data.rejection_reason) updateData.rejectionReason = data.rejection_reason;
    } else if (data.status === 'flagged' && !reviews[index].flaggedAt) {
      updateData.flaggedAt = new Date().toISOString();
      if (data.flag_reason) updateData.flagReason = data.flag_reason;
    }

    reviews[index] = { ...reviews[index], ...updateData };
    writeJsonFile('reviews.json', reviews);
    return transformReview(reviews[index]);
  }

  static delete(id: string) {
    const reviews = readJsonFile<any>('reviews.json');
    const index = reviews.findIndex((r: any) => r.id === id);
    if (index === -1) return false;

    reviews.splice(index, 1);
    writeJsonFile('reviews.json', reviews);
    return true;
  }
}
