import { NextResponse } from "next/server"
import { TransactionModel, OrderModel, UserModel } from "../../../../lib/database/models"

// GET /api/admin/transactions
export async function GET() {
  try {
    const transactions = TransactionModel.getAll()

    // Enrich transactions with order and user information
    const enrichedTransactions = transactions.map(transaction => {
      const order = OrderModel.getById(transaction.order_id)
      const user = UserModel.getById(transaction.user_id)

      return {
        id: transaction.id,
        orderId: transaction.order_id,
        userId: transaction.user_id,
        amount: transaction.amount,
        status: transaction.status,
        paymentMethod: transaction.payment_method,
        transactionFee: transaction.transaction_fee,
        refundAmount: transaction.refund_amount,
        failureReason: transaction.failure_reason,
        refundReason: transaction.refund_reason,
        cancellationReason: transaction.cancellation_reason,
        createdAt: transaction.created_at,
        completedAt: transaction.completed_at,
        failedAt: transaction.failed_at,
        refundedAt: transaction.refunded_at,
        cancelledAt: transaction.cancelled_at,
        order: order ? {
          id: order.id,
          status: order.status,
          totalAmount: order.total_amount
        } : null,
        user: user ? {
          id: user.id,
          name: user.name,
          email: user.email
        } : null
      }
    })
    return NextResponse.json(enrichedTransactions)
  } catch (error) {
    console.error("Error reading transactions:", error)
    return NextResponse.json(
      { error: "Failed to read transactions" },
      { status: 500 }
    )
  }
}

