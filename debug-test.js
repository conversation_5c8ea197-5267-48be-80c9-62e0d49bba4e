console.log('Current working directory:', process.cwd());
console.log('Node version:', process.version);
console.log('Platform:', process.platform);
console.log('Architecture:', process.arch);

console.log('\nTesting module resolution...');
try {
  const path = require.resolve('better-sqlite3');
  console.log('better-sqlite3 resolved to:', path);
} catch (error) {
  console.log('better-sqlite3 not found:', error.message);
}

console.log('\nTesting require...');
try {
  const Database = require('better-sqlite3');
  console.log('better-sqlite3 loaded successfully, type:', typeof Database);
} catch (error) {
  console.log('better-sqlite3 require failed:', error.message);
  console.log('Error code:', error.code);
}
