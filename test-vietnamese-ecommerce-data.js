// Test comprehensive Vietnamese e-commerce data
const fs = require('fs');
const path = require('path');

console.log('🇻🇳 Testing comprehensive Vietnamese e-commerce data...\n');

const dbPath = path.join(process.cwd(), 'data', 'store.json');

function loadDatabase() {
  return JSON.parse(fs.readFileSync(dbPath, 'utf-8'));
}

try {
  console.log('📊 Loading enhanced Vietnamese e-commerce database...');
  const db = loadDatabase();
  
  console.log('✓ Database loaded successfully');
  console.log(`   Categories: ${db.categories.length}`);
  console.log(`   Users: ${db.users.length}`);
  console.log(`   Products: ${db.products.length}`);
  console.log(`   Reviews: ${db.reviews.length}`);
  console.log(`   Orders: ${db.orders.length}`);
  console.log(`   Transactions: ${db.transactions.length}`);

  // Test 1: Vietnamese Product Names and Descriptions
  console.log('\n1. 📱 Testing Vietnamese Product Names and Descriptions...');
  const vietnameseProducts = db.products.filter(p => 
    p.name.match(/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i) ||
    p.description.includes('với') || p.description.includes('và') || p.description.includes('màn hình')
  );
  
  console.log(`✓ Found ${vietnameseProducts.length} products with Vietnamese names/descriptions`);
  if (vietnameseProducts.length > 0) {
    console.log(`   Sample: ${vietnameseProducts[0].name}`);
    console.log(`   Description: ${vietnameseProducts[0].description.substring(0, 80)}...`);
  }

  // Test 2: VND Pricing Analysis
  console.log('\n2. 💰 Testing VND Pricing Structure...');
  const priceRanges = {
    'Under 5M': db.products.filter(p => p.price < 5000000).length,
    '5M-10M': db.products.filter(p => p.price >= 5000000 && p.price < 10000000).length,
    '10M-20M': db.products.filter(p => p.price >= 10000000 && p.price < 20000000).length,
    '20M-30M': db.products.filter(p => p.price >= 20000000 && p.price < 30000000).length,
    'Over 30M': db.products.filter(p => p.price >= 30000000).length
  };
  
  console.log('✓ VND Price Distribution:');
  Object.entries(priceRanges).forEach(([range, count]) => {
    console.log(`   ${range} VND: ${count} products`);
  });

  // Test 3: Promotional Products Analysis
  console.log('\n3. 🏷️ Testing Promotional Products...');
  const promotionalProducts = db.products.filter(p => p.is_promotional);
  const totalSavings = promotionalProducts.reduce((sum, p) => {
    return sum + (p.original_price - p.price);
  }, 0);
  
  console.log(`✓ Found ${promotionalProducts.length} promotional products`);
  console.log(`✓ Total savings available: ${totalSavings.toLocaleString()} VND`);
  
  if (promotionalProducts.length > 0) {
    const bestDeal = promotionalProducts.reduce((best, current) => {
      const currentDiscount = ((current.original_price - current.price) / current.original_price) * 100;
      const bestDiscount = ((best.original_price - best.price) / best.original_price) * 100;
      return currentDiscount > bestDiscount ? current : best;
    });
    
    const discountPercent = ((bestDeal.original_price - bestDeal.price) / bestDeal.original_price) * 100;
    console.log(`✓ Best deal: ${bestDeal.name} - ${discountPercent.toFixed(1)}% off`);
  }

  // Test 4: Vietnamese User Data
  console.log('\n4. 👥 Testing Vietnamese User Data...');
  const vietnameseNames = ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Vũ', 'Đặng', 'Bùi', 'Ngô', 'Lý', 'Trương'];
  const usersWithVietnameseNames = db.users.filter(u => 
    vietnameseNames.some(name => u.name.includes(name))
  );
  
  console.log(`✓ Found ${usersWithVietnameseNames.length} users with Vietnamese names`);
  
  // Test Vietnamese addresses
  const vietnameseCities = ['Hồ Chí Minh', 'Hà Nội', 'Đà Nẵng', 'Cần Thơ', 'Hải Phòng', 'Huế'];
  const usersWithVietnameseAddresses = db.user_addresses.filter(addr => 
    vietnameseCities.some(city => addr.city.includes(city))
  );
  
  console.log(`✓ Found ${usersWithVietnameseAddresses.length} Vietnamese addresses`);
  console.log(`   Cities: ${[...new Set(usersWithVietnameseAddresses.map(a => a.city))].join(', ')}`);

  // Test 5: Vietnamese Payment Methods
  console.log('\n5. 💳 Testing Vietnamese Payment Methods...');
  const vietnamesePaymentMethods = ['vnpay', 'momo', 'zalopay', 'bank_transfer', 'cod'];
  const paymentMethodCounts = {};
  
  db.transactions.forEach(t => {
    paymentMethodCounts[t.payment_method] = (paymentMethodCounts[t.payment_method] || 0) + 1;
  });
  
  console.log('✓ Payment method distribution:');
  Object.entries(paymentMethodCounts).forEach(([method, count]) => {
    const methodLabels = {
      'vnpay': 'VNPay',
      'momo': 'MoMo',
      'zalopay': 'ZaloPay',
      'bank_transfer': 'Chuyển khoản ngân hàng',
      'cod': 'Thanh toán khi nhận hàng',
      'credit_card': 'Thẻ tín dụng'
    };
    console.log(`   ${methodLabels[method] || method}: ${count} transactions`);
  });

  // Test 6: Vietnamese Reviews and Comments
  console.log('\n6. 💬 Testing Vietnamese Reviews and Comments...');
  const vietnameseReviews = db.reviews.filter(r => 
    r.comment.includes('tuyệt vời') || r.comment.includes('đẹp') || r.comment.includes('tốt') ||
    r.comment.includes('mượt') || r.comment.includes('ấn tượng') || r.comment.includes('đáng')
  );
  
  console.log(`✓ Found ${vietnameseReviews.length} reviews with Vietnamese expressions`);
  
  // Average rating analysis
  const averageRating = db.reviews.reduce((sum, r) => sum + r.rating, 0) / db.reviews.length;
  console.log(`✓ Average product rating: ${averageRating.toFixed(2)}/5`);
  
  // Review status distribution
  const reviewStatuses = {};
  db.reviews.forEach(r => {
    reviewStatuses[r.status] = (reviewStatuses[r.status] || 0) + 1;
  });
  
  console.log('✓ Review status distribution:');
  Object.entries(reviewStatuses).forEach(([status, count]) => {
    const statusLabels = {
      'approved': 'Đã duyệt',
      'pending': 'Chờ duyệt',
      'rejected': 'Từ chối'
    };
    console.log(`   ${statusLabels[status] || status}: ${count} reviews`);
  });

  // Test 7: Transaction Status and Amounts
  console.log('\n7. 💸 Testing Transaction Status and Amounts...');
  const transactionStatuses = {};
  let totalTransactionAmount = 0;
  let totalFees = 0;
  
  db.transactions.forEach(t => {
    transactionStatuses[t.status] = (transactionStatuses[t.status] || 0) + 1;
    totalTransactionAmount += t.amount;
    totalFees += (t.transaction_fee || 0);
  });
  
  console.log('✓ Transaction status distribution:');
  Object.entries(transactionStatuses).forEach(([status, count]) => {
    const statusLabels = {
      'completed': 'Hoàn thành',
      'pending': 'Đang xử lý',
      'failed': 'Thất bại',
      'refunded': 'Đã hoàn tiền',
      'cancelled': 'Đã hủy'
    };
    console.log(`   ${statusLabels[status] || status}: ${count} transactions`);
  });
  
  console.log(`✓ Total transaction volume: ${totalTransactionAmount.toLocaleString()} VND`);
  console.log(`✓ Total transaction fees: ${totalFees.toLocaleString()} VND`);

  // Test 8: Order Analysis
  console.log('\n8. 📦 Testing Order Analysis...');
  const orderStatuses = {};
  let totalOrderValue = 0;
  
  db.orders.forEach(o => {
    orderStatuses[o.status] = (orderStatuses[o.status] || 0) + 1;
    totalOrderValue += o.totalAmount;
  });
  
  console.log('✓ Order status distribution:');
  Object.entries(orderStatuses).forEach(([status, count]) => {
    const statusLabels = {
      'delivered': 'Đã giao',
      'processing': 'Đang xử lý',
      'shipped': 'Đang giao',
      'cancelled': 'Đã hủy',
      'pending': 'Chờ xử lý'
    };
    console.log(`   ${statusLabels[status] || status}: ${count} orders`);
  });
  
  console.log(`✓ Total order value: ${totalOrderValue.toLocaleString()} VND`);
  console.log(`✓ Average order value: ${(totalOrderValue / db.orders.length).toLocaleString()} VND`);

  // Test 9: Product Categories Analysis
  console.log('\n9. 📱 Testing Product Categories...');
  const categoryDistribution = {};
  
  db.products.forEach(p => {
    const categoryId = p.category_id;
    const category = db.categories.find(c => c.id === categoryId);
    const categoryName = category ? category.name : categoryId;
    categoryDistribution[categoryName] = (categoryDistribution[categoryName] || 0) + 1;
  });
  
  console.log('✓ Product distribution by category:');
  Object.entries(categoryDistribution).forEach(([category, count]) => {
    console.log(`   ${category}: ${count} products`);
  });

  // Test 10: Data Consistency Check
  console.log('\n10. 🔍 Testing Data Consistency...');
  
  // Check foreign key relationships
  let validOrderItems = 0;
  let invalidOrderItems = 0;
  
  db.order_items.forEach(item => {
    const order = db.orders.find(o => o.id === item.order_id);
    const product = db.products.find(p => p.id === item.product_id);
    
    if (order && product) {
      validOrderItems++;
    } else {
      invalidOrderItems++;
    }
  });
  
  console.log(`✓ Order items consistency: ${validOrderItems} valid, ${invalidOrderItems} invalid`);
  
  // Check transaction-order relationships
  let validTransactions = 0;
  let invalidTransactions = 0;
  
  db.transactions.forEach(trans => {
    const order = db.orders.find(o => o.id === trans.order_id);
    const user = db.users.find(u => u.id === trans.user_id);
    
    if (order && user) {
      validTransactions++;
    } else {
      invalidTransactions++;
    }
  });
  
  console.log(`✓ Transaction relationships: ${validTransactions} valid, ${invalidTransactions} invalid`);

  console.log('\n🎉 Vietnamese e-commerce data analysis completed successfully!');
  console.log('✅ Comprehensive Vietnamese product catalog with authentic names');
  console.log('✅ Realistic VND pricing structure across all categories');
  console.log('✅ Diverse Vietnamese user base with proper addresses');
  console.log('✅ Vietnamese payment methods (VNPay, MoMo, ZaloPay, etc.)');
  console.log('✅ Authentic Vietnamese product reviews and comments');
  console.log('✅ Proper foreign key relationships maintained');
  console.log('✅ Rich e-commerce ecosystem with orders, transactions, and reviews');

} catch (error) {
  console.error('❌ Vietnamese e-commerce data test failed:', error.message);
  console.error('Stack:', error.stack);
}
