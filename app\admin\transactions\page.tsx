"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Search, Eye, Check, X } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"

interface Transaction {
  id: string
  orderId: string
  userId: string
  amount: number
  status: string
  paymentMethod: string
  transactionFee: number
  refundAmount?: number
  failureReason?: string
  refundReason?: string
  cancellationReason?: string
  createdAt: string
  completedAt?: string
  failedAt?: string
  refundedAt?: string
  cancelledAt?: string
}

export default function AdminTransactions() {
  const router = useRouter()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        const response = await fetch("/api/admin/transactions")
        if (!response.ok) {
          throw new Error("Failed to fetch transactions")
        }
        const data = await response.json()
        setTransactions(data)
      } catch (error) {
        console.error("Error fetching transactions:", error)
        toast({
          title: "Lỗi",
          description: "Không thể tải danh sách giao dịch.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchTransactions()
  }, [])

  const handleStatusChange = async (transactionId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/transactions/${transactionId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error("Failed to update transaction status")
      }

      setTransactions(transactions.map(transaction =>
        transaction.id === transactionId
          ? { ...transaction, status: newStatus }
          : transaction
      ))

      toast({
        title: "Thành công",
        description: "Cập nhật trạng thái giao dịch thành công.",
      })
    } catch (error) {
      console.error("Error updating transaction status:", error)
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật trạng thái giao dịch.",
        variant: "destructive",
      })
    }
  }

  const filteredTransactions = transactions.filter(transaction =>
    transaction.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
    transaction.userId.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Quản lý giao dịch</h1>
        <p className="text-muted-foreground">
          Theo dõi và quản lý các giao dịch thanh toán
        </p>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm theo mã đơn hàng hoặc ID người dùng..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Mã đơn hàng</TableHead>
              <TableHead>Người dùng</TableHead>
              <TableHead>Số tiền</TableHead>
              <TableHead>Phương thức</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead>Ngày tạo</TableHead>
              <TableHead className="text-right">Thao tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTransactions.map((transaction) => (
              <TableRow key={transaction.id}>
                <TableCell>{transaction.orderId}</TableCell>
                <TableCell>{transaction.userId}</TableCell>
                <TableCell>{formatCurrency(transaction.amount)}</TableCell>
                <TableCell>{transaction.paymentMethod}</TableCell>
                <TableCell>
                  <span
                    className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                      transaction.status === "completed"
                        ? "bg-green-100 text-green-800"
                        : transaction.status === "pending"
                        ? "bg-yellow-100 text-yellow-800"
                        : transaction.status === "refunded"
                        ? "bg-blue-100 text-blue-800"
                        : transaction.status === "cancelled"
                        ? "bg-gray-100 text-gray-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {transaction.status === "completed"
                      ? "Hoàn thành"
                      : transaction.status === "pending"
                      ? "Đang xử lý"
                      : transaction.status === "refunded"
                      ? "Đã hoàn tiền"
                      : transaction.status === "cancelled"
                      ? "Đã hủy"
                      : "Thất bại"}
                  </span>
                </TableCell>
                <TableCell>
                  {new Date(transaction.createdAt).toLocaleDateString("vi-VN")}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        Thao tác
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => router.push(`/admin/transactions/${transaction.id}`)}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Xem chi tiết
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleStatusChange(transaction.id, "completed")}
                      >
                        <Check className="mr-2 h-4 w-4" />
                        Xác nhận
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleStatusChange(transaction.id, "failed")}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Hủy
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 