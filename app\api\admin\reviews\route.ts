import { NextResponse } from "next/server"
import { ReviewModel } from "../../../../lib/database/models"

// GET /api/admin/reviews
export async function GET() {
  try {
    const reviews = ReviewModel.getAll()

    // Transform to frontend format
    const transformedReviews = reviews.map(review => ({
      id: review.id,
      productId: review.product_id,
      userId: review.user_id,
      rating: review.rating,
      comment: review.comment,
      status: review.status,
      helpfulVotes: review.helpful_votes,
      unhelpfulVotes: review.unhelpful_votes,
      flagReason: review.flag_reason,
      rejectionReason: review.rejection_reason,
      createdAt: review.created_at,
      approvedAt: review.approved_at,
      rejectedAt: review.rejected_at,
      flaggedAt: review.flagged_at
    }))

    return NextResponse.json(transformedReviews)
  } catch (error) {
    console.error("Error fetching reviews:", error)
    return NextResponse.json(
      { error: "Failed to fetch reviews" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/reviews/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status } = await request.json()

    const updatedReview = ReviewModel.update(id, { status })

    if (!updatedReview) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error updating review:", error)
    return NextResponse.json(
      { error: "Failed to update review" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/reviews/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const deleted = ReviewModel.delete(id)

    if (!deleted) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting review:", error)
    return NextResponse.json(
      { error: "Failed to delete review" },
      { status: 500 }
    )
  }
}