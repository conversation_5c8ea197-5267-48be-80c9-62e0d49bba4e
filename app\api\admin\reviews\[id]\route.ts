import { NextResponse } from "next/server"
import { ReviewModel } from "../../../../../lib/database/models"

// PATCH /api/admin/reviews/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status, rejectionReason, flagReason } = await request.json()

    const updateData: any = { status }
    if (rejectionReason) updateData.rejection_reason = rejectionReason
    if (flagReason) updateData.flag_reason = flagReason

    const updatedReview = ReviewModel.update(id, updateData)

    if (!updatedReview) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      id: updatedReview.id,
      productId: updatedReview.product_id,
      userId: updatedReview.user_id,
      rating: updatedReview.rating,
      comment: updatedReview.comment,
      status: updatedReview.status,
      helpfulVotes: updatedReview.helpful_votes,
      unhelpfulVotes: updatedReview.unhelpful_votes,
      flagReason: updatedReview.flag_reason,
      rejectionReason: updatedReview.rejection_reason,
      createdAt: updatedReview.created_at,
      approvedAt: updatedReview.approved_at,
      rejectedAt: updatedReview.rejected_at,
      flaggedAt: updatedReview.flagged_at
    })
  } catch (error) {
    console.error("Error updating review:", error)
    return NextResponse.json(
      { error: "Failed to update review" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/reviews/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const deleted = ReviewModel.delete(id)

    if (!deleted) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: "Review deleted successfully" })
  } catch (error) {
    console.error("Error deleting review:", error)
    return NextResponse.json(
      { error: "Failed to delete review" },
      { status: 500 }
    )
  }
}
