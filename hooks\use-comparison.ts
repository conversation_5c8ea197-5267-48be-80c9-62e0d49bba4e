"use client"

import { useState, useEffect } from "react"
import { toast } from "@/components/ui/use-toast"
import type { Product } from "@/lib/types"

interface ComparisonState {
  items: Product[]
  isLoading: boolean
}

const MAX_COMPARISON_ITEMS = 4

export function useComparison() {
  const [comparison, setComparison] = useState<ComparisonState>({
    items: [],
    isLoading: false,
  })

  // Load comparison from localStorage
  const loadComparison = () => {
    if (typeof window !== 'undefined') {
      const comparisonData = localStorage.getItem('product_comparison')
      if (comparisonData) {
        try {
          const items = JSON.parse(comparisonData)
          setComparison({ items, isLoading: false })
        } catch (error) {
          console.error("Error parsing comparison data:", error)
          setComparison({ items: [], isLoading: false })
        }
      }
    }
  }

  // Save comparison to localStorage
  const saveComparison = (items: Product[]) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('product_comparison', JSON.stringify(items))
    }
  }

  // Check if product is in comparison
  const isInComparison = (productId: string): boolean => {
    return comparison.items.some(item => item.id === productId)
  }

  // Add product to comparison
  const addToComparison = async (product: Product) => {
    if (isInComparison(product.id)) {
      toast({
        title: "Sản phẩm đã có trong danh sách so sánh",
        description: "Sản phẩm này đã có trong danh sách so sánh của bạn.",
        variant: "destructive",
      })
      return false
    }

    if (comparison.items.length >= MAX_COMPARISON_ITEMS) {
      toast({
        title: "Danh sách so sánh đã đầy",
        description: `Bạn chỉ có thể so sánh tối đa ${MAX_COMPARISON_ITEMS} sản phẩm. Vui lòng xóa một sản phẩm khác trước.`,
        variant: "destructive",
      })
      return false
    }

    try {
      const newItems = [...comparison.items, product]
      setComparison({ items: newItems, isLoading: false })
      saveComparison(newItems)
      
      toast({
        title: "Đã thêm vào so sánh",
        description: `${product.name} đã được thêm vào danh sách so sánh (${newItems.length}/${MAX_COMPARISON_ITEMS}).`,
      })
      return true
    } catch (error) {
      console.error("Error adding to comparison:", error)
      toast({
        title: "Lỗi",
        description: "Không thể thêm sản phẩm vào danh sách so sánh.",
        variant: "destructive",
      })
      return false
    }
  }

  // Remove product from comparison
  const removeFromComparison = async (productId: string) => {
    try {
      const newItems = comparison.items.filter(item => item.id !== productId)
      setComparison({ items: newItems, isLoading: false })
      saveComparison(newItems)
      
      const removedProduct = comparison.items.find(item => item.id === productId)
      toast({
        title: "Đã xóa khỏi so sánh",
        description: `${removedProduct?.name || "Sản phẩm"} đã được xóa khỏi danh sách so sánh.`,
      })
      return true
    } catch (error) {
      console.error("Error removing from comparison:", error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa sản phẩm khỏi danh sách so sánh.",
        variant: "destructive",
      })
      return false
    }
  }

  // Toggle product in comparison
  const toggleComparison = async (product: Product) => {
    if (isInComparison(product.id)) {
      return await removeFromComparison(product.id)
    } else {
      return await addToComparison(product)
    }
  }

  // Clear comparison
  const clearComparison = async () => {
    try {
      setComparison({ items: [], isLoading: false })
      saveComparison([])
      
      toast({
        title: "Đã xóa danh sách so sánh",
        description: "Tất cả sản phẩm đã được xóa khỏi danh sách so sánh.",
      })
      return true
    } catch (error) {
      console.error("Error clearing comparison:", error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa danh sách so sánh.",
        variant: "destructive",
      })
      return false
    }
  }

  // Get comparison count
  const getComparisonCount = (): number => {
    return comparison.items.length
  }

  // Check if comparison is full
  const isComparisonFull = (): boolean => {
    return comparison.items.length >= MAX_COMPARISON_ITEMS
  }

  // Load comparison on mount
  useEffect(() => {
    loadComparison()
  }, [])

  return {
    comparison: comparison.items,
    isLoading: comparison.isLoading,
    isInComparison,
    addToComparison,
    removeFromComparison,
    toggleComparison,
    clearComparison,
    getComparisonCount,
    isComparisonFull,
    maxItems: MAX_COMPARISON_ITEMS,
    refreshComparison: loadComparison,
  }
}
