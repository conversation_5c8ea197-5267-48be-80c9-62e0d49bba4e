import path from 'path';
import fs from 'fs';

// Type definition for Database
type Database = any;

// Database file path
const DB_PATH = path.join(process.cwd(), 'data', 'store.db');

// Create database connection
let db: Database | null = null;
let isInitializing = false;

// Enhanced file-based database for fallback when better-sqlite3 is not available
function createFileBasedDatabase() {
  console.warn('Using file-based database - better-sqlite3 not available');

  // Ensure data directory exists
  const dataDir = path.dirname(DB_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  // Create a simple file-based database that mimics SQLite interface
  const dbFile = DB_PATH.replace('.db', '.json');

  // Initialize database structure if it doesn't exist
  if (!fs.existsSync(dbFile)) {
    const initialData = {
      categories: [],
      users: [],
      user_addresses: [],
      products: [],
      reviews: [],
      orders: [],
      order_items: [],
      order_shipping_addresses: [],
      transactions: [],
      cart_items: [],
      _meta: {
        created: new Date().toISOString(),
        version: '1.0.0'
      }
    };
    fs.writeFileSync(dbFile, JSON.stringify(initialData, null, 2));
  }

  // Load data from file
  function loadData() {
    try {
      const data = fs.readFileSync(dbFile, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error loading database file:', error);
      return { categories: [], users: [], user_addresses: [], products: [], reviews: [], orders: [], order_items: [], order_shipping_addresses: [], transactions: [], cart_items: [] };
    }
  }

  // Save data to file
  function saveData(data: any) {
    try {
      fs.writeFileSync(dbFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error saving database file:', error);
    }
  }

  return {
    pragma: () => {},
    exec: (sql: string) => {
      console.log('File-based DB exec:', sql);
    },
    prepare: (sql: string) => {
      return {
        run: (...params: any[]) => {
          console.log('File-based DB run:', sql, params);
          return { changes: 1, lastInsertRowid: Date.now() };
        },
        get: (...params: any[]) => {
          console.log('File-based DB get:', sql, params);
          return null;
        },
        all: (...params: any[]) => {
          console.log('File-based DB all:', sql, params);
          return [];
        },
      };
    },
    transaction: (fn: Function) => {
      return fn();
    },
    close: () => {},
    // Custom methods for file-based operations
    _loadData: loadData,
    _saveData: saveData,
    _isFileBased: true
  };
}

// Server-side only database initialization
function initializeDatabase() {
  if (typeof window !== 'undefined') {
    throw new Error('Database can only be used on the server side');
  }

  if (db || isInitializing) {
    return db;
  }

  isInitializing = true;

  try {
    // Try to load better-sqlite3, fallback to mock if not available
    let Database;
    try {
      Database = require('better-sqlite3');
      console.log('✓ better-sqlite3 loaded successfully');
    } catch (e) {
      console.warn('better-sqlite3 not available, using fallback mode');
      console.warn('Error details:', e.message);
      console.warn('Error code:', e.code);
      console.warn('Current working directory:', process.cwd());

      // Check if module exists
      try {
        const path = require.resolve('better-sqlite3');
        console.warn('Module path found:', path);
      } catch (resolveError) {
        console.warn('Module not found in node_modules');
      }

      // Return a file-based database for development
      isInitializing = false;
      db = createFileBasedDatabase();
      return db;
    }

    // Ensure data directory exists
    const dataDir = path.dirname(DB_PATH);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    db = new Database(DB_PATH);

    // Enable foreign key constraints
    db.pragma('foreign_keys = ON');

    // Set journal mode to WAL for better performance
    db.pragma('journal_mode = WAL');

    // Initialize database schema
    initializeSchema();

    isInitializing = false;
    return db;
  } catch (error) {
    isInitializing = false;
    console.error('Failed to initialize database:', error);
    // Fallback to file-based database
    db = createFileBasedDatabase();
    return db;
  }
}

export function getDatabase(): Database {
  if (!db) {
    initializeDatabase();
  }
  return db;
}

function initializeSchema() {
  if (!db) return;

  const schemaPath = path.join(process.cwd(), 'lib', 'database', 'schema.sql');

  if (fs.existsSync(schemaPath)) {
    const schema = fs.readFileSync(schemaPath, 'utf-8');

    // Split schema by semicolons and execute each statement
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    for (const statement of statements) {
      try {
        db.exec(statement);
      } catch (error) {
        console.error('Error executing schema statement:', statement);
        console.error(error);
      }
    }
  }
}

// Close database connection
export function closeDatabase() {
  if (db) {
    db.close();
    db = null;
  }
}

// Utility function to run migrations
export function runMigration(migrationSql: string) {
  const database = getDatabase();

  try {
    database.exec(migrationSql);
    console.log('Migration executed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Transaction wrapper
export function withTransaction<T>(callback: (db: Database) => T): T {
  const database = getDatabase();

  if (database._isFileBased) {
    // For file-based database, just execute the callback
    // File operations are atomic at the OS level
    return callback(database);
  }

  const transaction = database.transaction(() => {
    return callback(database);
  });

  return transaction();
}

// Graceful shutdown
process.on('exit', closeDatabase);
process.on('SIGINT', closeDatabase);
process.on('SIGTERM', closeDatabase);
