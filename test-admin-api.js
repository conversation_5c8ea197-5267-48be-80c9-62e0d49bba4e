// Test admin API endpoints
const { ProductModel, CategoryModel } = require('./lib/database/models');

console.log('🧪 Testing Admin API functionality...\n');

try {
  // Test 1: Get all products (simulating GET /api/admin/products)
  console.log('1. Testing GET all products...');
  const allProducts = ProductModel.getAll({
    orderBy: 'created_at',
    orderDirection: 'DESC'
  });
  console.log(`✓ Found ${allProducts.length} products`);
  
  if (allProducts.length > 0) {
    const firstProduct = allProducts[0];
    console.log(`   First product: ${firstProduct.name} (${firstProduct.id})`);
    console.log(`   Category ID: ${firstProduct.category_id}`);
    console.log(`   Price: ${firstProduct.price.toLocaleString()} VND`);
    console.log(`   Stock: ${firstProduct.stock}`);
  }

  // Test 2: Get categories for proper transformation
  console.log('\n2. Testing GET categories...');
  const categories = CategoryModel.getAll();
  console.log(`✓ Found ${categories.length} categories`);
  
  if (categories.length > 0) {
    console.log(`   Categories: ${categories.map(c => c.name).join(', ')}`);
  }

  // Test 3: Get specific product by ID (simulating GET /api/admin/products/[id])
  if (allProducts.length > 0) {
    console.log('\n3. Testing GET product by ID...');
    const testProductId = allProducts[0].id;
    const product = ProductModel.getById(testProductId);
    
    if (product) {
      console.log(`✓ Found product: ${product.name}`);
      console.log(`   ID: ${product.id}`);
      console.log(`   Description: ${product.description?.substring(0, 50)}...`);
    } else {
      console.log('✗ Product not found');
    }
  }

  // Test 4: Update product (simulating PATCH /api/admin/products/[id])
  if (allProducts.length > 0) {
    console.log('\n4. Testing PATCH product update...');
    const testProductId = allProducts[0].id;
    const originalProduct = ProductModel.getById(testProductId);
    
    // Test status update
    const statusUpdateData = {
      description: `[pending] ${originalProduct.description || ''}`
    };
    
    const updatedProduct = ProductModel.update(testProductId, statusUpdateData);
    
    if (updatedProduct) {
      console.log(`✓ Product updated successfully`);
      console.log(`   Updated description: ${updatedProduct.description?.substring(0, 50)}...`);
      
      // Restore original description
      ProductModel.update(testProductId, { description: originalProduct.description });
      console.log(`✓ Product restored to original state`);
    } else {
      console.log('✗ Product update failed');
    }
  }

  // Test 5: Create new product (simulating POST /api/admin/products)
  console.log('\n5. Testing POST create product...');
  
  const newProductData = {
    id: `test-prod-${Date.now()}`,
    name: 'Test Admin Product',
    description: 'A test product created via admin API',
    price: 500000,
    original_price: 600000,
    image: 'https://example.com/test-product.jpg',
    images: JSON.stringify(['https://example.com/test-product.jpg']),
    category_id: categories.length > 0 ? categories[0].id : 'test-category',
    stock: 10,
    rating: 0,
    review_count: 0,
    sold_count: 0,
    featured: false,
    is_promotional: false,
    promotion_ends: null
  };
  
  const createdProduct = ProductModel.create(newProductData);
  
  if (createdProduct) {
    console.log(`✓ Product created successfully`);
    console.log(`   Created product: ${createdProduct.name} (${createdProduct.id})`);
    
    // Test 6: Delete the test product (simulating DELETE /api/admin/products/[id])
    console.log('\n6. Testing DELETE product...');
    const deleted = ProductModel.delete(createdProduct.id);
    
    if (deleted) {
      console.log(`✓ Product deleted successfully`);
    } else {
      console.log('✗ Product deletion failed');
    }
  } else {
    console.log('✗ Product creation failed');
  }

  // Test 7: Test product transformation (what the API returns)
  console.log('\n7. Testing product transformation...');
  
  if (allProducts.length > 0) {
    const dbProduct = allProducts[0];
    
    // Extract status from description
    function extractStatusFromDescription(description) {
      if (!description) return 'active';
      const match = description.match(/^\[(\w+)\]/);
      return match ? match[1] : 'active';
    }
    
    // Clean description
    function cleanDescription(description) {
      if (!description) return '';
      return description.replace(/^\[\w+\]\s*/, '');
    }
    
    // Transform product
    const category = categories.find(c => c.id === dbProduct.category_id) || {
      id: dbProduct.category_id,
      name: dbProduct.category_id,
      slug: dbProduct.category_id
    };
    
    const transformedProduct = {
      id: dbProduct.id,
      name: dbProduct.name,
      description: cleanDescription(dbProduct.description || ''),
      price: dbProduct.price,
      originalPrice: dbProduct.original_price,
      image: dbProduct.image,
      images: dbProduct.images ? JSON.parse(dbProduct.images) : [],
      category: {
        id: category.id,
        name: category.name,
        slug: category.slug
      },
      stock: dbProduct.stock,
      rating: dbProduct.rating,
      reviewCount: dbProduct.review_count,
      soldCount: dbProduct.sold_count,
      featured: Boolean(dbProduct.featured),
      isPromotional: Boolean(dbProduct.is_promotional),
      promotionEnds: dbProduct.promotion_ends,
      status: extractStatusFromDescription(dbProduct.description),
      createdAt: dbProduct.created_at
    };
    
    console.log(`✓ Product transformation successful`);
    console.log(`   Transformed product: ${transformedProduct.name}`);
    console.log(`   Category: ${transformedProduct.category.name}`);
    console.log(`   Status: ${transformedProduct.status}`);
    console.log(`   Images: ${transformedProduct.images.length} images`);
  }

  // Test 8: Test specific product ID that was mentioned (prod-037)
  console.log('\n8. Testing specific product prod-037...');
  const prod037 = ProductModel.getById('prod-037');
  
  if (prod037) {
    console.log(`✓ Found prod-037: ${prod037.name}`);
    console.log(`   Price: ${prod037.price.toLocaleString()} VND`);
    console.log(`   Stock: ${prod037.stock}`);
    console.log(`   Category: ${prod037.category_id}`);
    
    // Test updating prod-037
    const testUpdate = ProductModel.update('prod-037', { stock: prod037.stock + 1 });
    if (testUpdate) {
      console.log(`✓ Successfully updated prod-037 stock to ${testUpdate.stock}`);
      // Restore original stock
      ProductModel.update('prod-037', { stock: prod037.stock });
      console.log(`✓ Restored original stock`);
    }
  } else {
    console.log('✗ Product prod-037 not found');
  }

  console.log('\n🎉 All admin API tests completed successfully!');
  console.log('✅ Admin product management should work correctly');

} catch (error) {
  console.error('❌ Admin API test failed:', error.message);
  console.error('Stack:', error.stack);
}
