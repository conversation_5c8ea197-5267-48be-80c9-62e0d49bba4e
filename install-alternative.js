const { execSync } = require('child_process');
const fs = require('fs');

console.log('Attempting to install better-sqlite3 with different methods...');

// Method 1: Try npm install with specific flags
const methods = [
  'npm install better-sqlite3 --build-from-source',
  'npm install better-sqlite3 --force',
  'npm install better-sqlite3 --no-optional',
  'npm rebuild better-sqlite3',
  'npm install sqlite3', // Alternative library
  'npm install sql.js', // Another alternative
];

for (const method of methods) {
  console.log(`\nTrying: ${method}`);
  try {
    execSync(method, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✓ Success with: ${method}`);
    
    // Test if better-sqlite3 works now
    if (method.includes('better-sqlite3')) {
      try {
        const Database = require('better-sqlite3');
        console.log('✓ better-sqlite3 is now working!');
        process.exit(0);
      } catch (testError) {
        console.log('✗ Installation succeeded but module still not working');
      }
    }
  } catch (error) {
    console.log(`✗ Failed: ${error.message}`);
  }
}

console.log('\n❌ All methods failed. Checking for alternatives...');

// Check if we can use alternative libraries
const alternatives = ['sqlite3', 'sql.js'];
for (const alt of alternatives) {
  try {
    const lib = require(alt);
    console.log(`✓ Found alternative: ${alt}`);
  } catch (e) {
    console.log(`✗ ${alt} not available`);
  }
}
