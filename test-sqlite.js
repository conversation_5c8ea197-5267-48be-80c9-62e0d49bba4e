// Test script to check better-sqlite3 installation
console.log('Testing better-sqlite3 installation...');

try {
  const Database = require('better-sqlite3');
  console.log('✓ better-sqlite3 module loaded successfully');
  console.log('Database constructor type:', typeof Database);
  
  // Try to create a test database
  const db = new Database(':memory:');
  console.log('✓ In-memory database created successfully');
  
  // Test basic operations
  db.exec('CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)');
  console.log('✓ Table created successfully');
  
  const insert = db.prepare('INSERT INTO test (name) VALUES (?)');
  const result = insert.run('test-name');
  console.log('✓ Insert operation successful, lastInsertRowid:', result.lastInsertRowid);
  
  const select = db.prepare('SELECT * FROM test');
  const rows = select.all();
  console.log('✓ Select operation successful, rows:', rows);
  
  db.close();
  console.log('✓ Database closed successfully');
  
  console.log('\n🎉 better-sqlite3 is working correctly!');
} catch (error) {
  console.error('❌ better-sqlite3 error:', error.message);
  console.error('Full error:', error);
  
  // Check if it's a module not found error
  if (error.code === 'MODULE_NOT_FOUND') {
    console.log('\n💡 Suggestion: Run "npm install better-sqlite3" to install the package');
  }
  
  // Check if it's a compilation error
  if (error.message.includes('node-gyp') || error.message.includes('rebuild')) {
    console.log('\n💡 Suggestion: You may need to install build tools or rebuild the native module');
    console.log('   Try: npm rebuild better-sqlite3');
  }
}
