import { NextResponse } from "next/server"
import { CartModel, ProductModel } from "../../../lib/database/models"

// Transform cart item for API response
function transformCartItem(cartItem: any, product: any) {
  return {
    id: cartItem.id,
    productId: cartItem.product_id,
    product: {
      id: product.id,
      name: product.name,
      price: product.price,
      originalPrice: product.original_price,
      image: product.image,
      stock: product.stock,
      category: {
        id: product.category_id,
        name: product.category_name || 'Unknown'
      }
    },
    quantity: cartItem.quantity,
    createdAt: cartItem.created_at,
    updatedAt: cartItem.updated_at
  }
}

// GET /api/cart - Get user's cart items
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Get cart items with product details
    const cartWithProducts = CartModel.getCartWithProducts(userId)
    
    // Transform for API response
    const transformedCart = cartWithProducts.map(item => 
      transformCartItem(item, item.product)
    )

    // Calculate totals
    const totalItems = CartModel.getTotalItems(userId)
    const totalAmount = CartModel.getTotalAmount(userId)

    return NextResponse.json({
      items: transformedCart,
      totalItems,
      totalAmount,
      count: transformedCart.length
    })
  } catch (error) {
    console.error('Error fetching cart:', error)
    return NextResponse.json(
      { error: 'Failed to fetch cart' },
      { status: 500 }
    )
  }
}

// POST /api/cart - Add item to cart
export async function POST(request: Request) {
  try {
    const { userId, productId, quantity = 1 } = await request.json()

    if (!userId || !productId) {
      return NextResponse.json(
        { error: 'User ID and Product ID are required' },
        { status: 400 }
      )
    }

    if (quantity < 1) {
      return NextResponse.json(
        { error: 'Quantity must be at least 1' },
        { status: 400 }
      )
    }

    // Check if product exists and has sufficient stock
    const product = ProductModel.getById(productId)
    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    if (product.stock < quantity) {
      return NextResponse.json(
        { error: `Only ${product.stock} items available in stock` },
        { status: 400 }
      )
    }

    // Check if item already exists in cart
    const existingCartItem = CartModel.getByUserAndProduct(userId, productId)
    
    let cartItem
    if (existingCartItem) {
      // Update quantity if item already exists
      const newQuantity = existingCartItem.quantity + quantity
      
      if (newQuantity > product.stock) {
        return NextResponse.json(
          { error: `Cannot add ${quantity} more items. Only ${product.stock - existingCartItem.quantity} more available.` },
          { status: 400 }
        )
      }
      
      cartItem = CartModel.updateQuantity(existingCartItem.id, newQuantity)
    } else {
      // Create new cart item
      cartItem = CartModel.create({
        id: `cart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        user_id: userId,
        product_id: productId,
        quantity
      })
    }

    if (!cartItem) {
      return NextResponse.json(
        { error: 'Failed to add item to cart' },
        { status: 500 }
      )
    }

    // Return the updated cart item with product details
    const transformedCartItem = transformCartItem(cartItem, product)

    return NextResponse.json({
      message: 'Item added to cart successfully',
      cartItem: transformedCartItem,
      totalItems: CartModel.getTotalItems(userId)
    })
  } catch (error) {
    console.error('Error adding to cart:', error)
    return NextResponse.json(
      { error: 'Failed to add item to cart' },
      { status: 500 }
    )
  }
}

// PUT /api/cart - Update cart item quantity
export async function PUT(request: Request) {
  try {
    const { cartItemId, quantity, userId } = await request.json()

    if (!cartItemId || !quantity || !userId) {
      return NextResponse.json(
        { error: 'Cart item ID, quantity, and user ID are required' },
        { status: 400 }
      )
    }

    if (quantity < 1) {
      return NextResponse.json(
        { error: 'Quantity must be at least 1' },
        { status: 400 }
      )
    }

    // Get cart item
    const cartItem = CartModel.getById(cartItemId)
    if (!cartItem || cartItem.user_id !== userId) {
      return NextResponse.json(
        { error: 'Cart item not found' },
        { status: 404 }
      )
    }

    // Check product stock
    const product = ProductModel.getById(cartItem.product_id)
    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    if (quantity > product.stock) {
      return NextResponse.json(
        { error: `Only ${product.stock} items available in stock` },
        { status: 400 }
      )
    }

    // Update quantity
    const updatedCartItem = CartModel.updateQuantity(cartItemId, quantity)
    
    if (!updatedCartItem) {
      return NextResponse.json(
        { error: 'Failed to update cart item' },
        { status: 500 }
      )
    }

    const transformedCartItem = transformCartItem(updatedCartItem, product)

    return NextResponse.json({
      message: 'Cart item updated successfully',
      cartItem: transformedCartItem,
      totalItems: CartModel.getTotalItems(userId)
    })
  } catch (error) {
    console.error('Error updating cart item:', error)
    return NextResponse.json(
      { error: 'Failed to update cart item' },
      { status: 500 }
    )
  }
}

// DELETE /api/cart - Remove item from cart
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const cartItemId = searchParams.get('cartItemId')
    const userId = searchParams.get('userId')

    if (!cartItemId || !userId) {
      return NextResponse.json(
        { error: 'Cart item ID and user ID are required' },
        { status: 400 }
      )
    }

    // Verify cart item belongs to user
    const cartItem = CartModel.getById(cartItemId)
    if (!cartItem || cartItem.user_id !== userId) {
      return NextResponse.json(
        { error: 'Cart item not found' },
        { status: 404 }
      )
    }

    // Delete cart item
    const deleted = CartModel.delete(cartItemId)
    
    if (!deleted) {
      return NextResponse.json(
        { error: 'Failed to remove item from cart' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Item removed from cart successfully',
      totalItems: CartModel.getTotalItems(userId)
    })
  } catch (error) {
    console.error('Error removing cart item:', error)
    return NextResponse.json(
      { error: 'Failed to remove item from cart' },
      { status: 500 }
    )
  }
}
