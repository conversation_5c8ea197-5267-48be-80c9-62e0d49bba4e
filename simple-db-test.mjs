// Simple database test using ES modules
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('Testing file-based database...');
console.log('Current directory:', process.cwd());

// Test creating the database file structure
const dataDir = join(process.cwd(), 'data');
const dbFile = join(dataDir, 'store.json');

console.log('Data directory:', dataDir);
console.log('Database file:', dbFile);

try {
  // Ensure data directory exists
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log('✓ Created data directory');
  } else {
    console.log('✓ Data directory exists');
  }

  // Create initial database structure
  const initialData = {
    categories: [],
    users: [],
    user_addresses: [],
    products: [],
    reviews: [],
    orders: [],
    order_items: [],
    order_shipping_addresses: [],
    transactions: [],
    _meta: {
      created: new Date().toISOString(),
      version: '1.0.0'
    }
  };

  fs.writeFileSync(dbFile, JSON.stringify(initialData, null, 2));
  console.log('✓ Created database file');

  // Test reading the file
  const data = JSON.parse(fs.readFileSync(dbFile, 'utf-8'));
  console.log('✓ Successfully read database file');
  console.log('Tables:', Object.keys(data).filter(key => !key.startsWith('_')));

  console.log('\n🎉 File-based database test successful!');

} catch (error) {
  console.error('❌ Test failed:', error.message);
}
