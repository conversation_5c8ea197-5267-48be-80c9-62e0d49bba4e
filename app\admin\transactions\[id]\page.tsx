"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON>, useParams } from "next/navigation"
import { ArrowLeft, CreditCard, User, Package, MapPin, Calendar, DollarSign } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"

interface TransactionDetails {
  id: string
  orderId: string
  userId: string
  amount: number
  status: string
  paymentMethod: string
  transactionFee: number
  refundAmount?: number
  failureReason?: string
  refundReason?: string
  cancellationReason?: string
  createdAt: string
  completedAt?: string
  failedAt?: string
  refundedAt?: string
  cancelledAt?: string
  order?: {
    id: string
    status: string
    totalAmount: number
    paymentMethod: string
    createdAt: string
    items: Array<{
      id: string
      productId: string
      productName: string
      productImage?: string
      quantity: number
      price: number
    }>
    shippingAddress?: {
      fullName: string
      phone: string
      address: string
      city: string
      district: string
      ward: string
    }
  }
  user?: {
    id: string
    name: string
    email: string
    phone: string
    role: string
  }
}

export default function TransactionDetails() {
  const router = useRouter()
  const params = useParams()
  const transactionId = params.id as string

  const [transaction, setTransaction] = useState<TransactionDetails | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchTransaction = async () => {
      try {
        const response = await fetch(`/api/admin/transactions/${transactionId}`)
        
        if (!response.ok) {
          if (response.status === 404) {
            toast({
              title: "Lỗi",
              description: "Không tìm thấy giao dịch.",
              variant: "destructive",
            })
            router.push("/admin/transactions")
            return
          }
          throw new Error("Failed to fetch transaction")
        }

        const data = await response.json()
        setTransaction(data)
      } catch (error) {
        console.error("Error fetching transaction:", error)
        toast({
          title: "Lỗi",
          description: "Không thể tải thông tin giao dịch.",
          variant: "destructive",
        })
        router.push("/admin/transactions")
      } finally {
        setLoading(false)
      }
    }

    if (transactionId) {
      fetchTransaction()
    }
  }, [transactionId, router])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { label: "Hoàn thành", variant: "default" as const, className: "bg-green-100 text-green-800" },
      pending: { label: "Đang xử lý", variant: "secondary" as const, className: "bg-yellow-100 text-yellow-800" },
      failed: { label: "Thất bại", variant: "destructive" as const, className: "bg-red-100 text-red-800" },
      refunded: { label: "Đã hoàn tiền", variant: "outline" as const, className: "bg-blue-100 text-blue-800" },
      cancelled: { label: "Đã hủy", variant: "outline" as const, className: "bg-gray-100 text-gray-800" },
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.label}
      </Badge>
    )
  }

  const getPaymentMethodLabel = (method: string) => {
    const methods: Record<string, string> = {
      'vnpay': 'VNPay',
      'momo': 'MoMo',
      'zalopay': 'ZaloPay',
      'bank_transfer': 'Chuyển khoản ngân hàng',
      'cod': 'Thanh toán khi nhận hàng',
      'credit_card': 'Thẻ tín dụng',
      'debit_card': 'Thẻ ghi nợ'
    }
    return methods[method] || method
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!transaction) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Không tìm thấy giao dịch</h2>
          <p className="text-muted-foreground mb-4">
            Giao dịch bạn đang tìm kiếm không tồn tại.
          </p>
          <Button onClick={() => router.push("/admin/transactions")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Quay lại danh sách
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.push("/admin/transactions")}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Quay lại
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Chi tiết giao dịch</h1>
          <p className="text-muted-foreground">
            Thông tin chi tiết giao dịch #{transaction.id}
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Transaction Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Thông tin giao dịch
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Mã giao dịch</p>
                <p className="font-mono">{transaction.id}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Trạng thái</p>
                {getStatusBadge(transaction.status)}
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Số tiền</p>
                <p className="text-lg font-semibold">{formatCurrency(transaction.amount)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Phí giao dịch</p>
                <p>{formatCurrency(transaction.transactionFee)}</p>
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-muted-foreground">Phương thức thanh toán</p>
              <p>{getPaymentMethodLabel(transaction.paymentMethod)}</p>
            </div>

            <div>
              <p className="text-sm font-medium text-muted-foreground">Ngày tạo</p>
              <p className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {new Date(transaction.createdAt).toLocaleString("vi-VN")}
              </p>
            </div>

            {transaction.completedAt && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Ngày hoàn thành</p>
                <p>{new Date(transaction.completedAt).toLocaleString("vi-VN")}</p>
              </div>
            )}

            {transaction.failedAt && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Ngày thất bại</p>
                <p>{new Date(transaction.failedAt).toLocaleString("vi-VN")}</p>
              </div>
            )}

            {transaction.refundedAt && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Ngày hoàn tiền</p>
                <p>{new Date(transaction.refundedAt).toLocaleString("vi-VN")}</p>
              </div>
            )}

            {transaction.refundAmount && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Số tiền hoàn</p>
                <p className="text-blue-600 font-semibold">{formatCurrency(transaction.refundAmount)}</p>
              </div>
            )}

            {transaction.failureReason && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Lý do thất bại</p>
                <p className="text-red-600">{transaction.failureReason}</p>
              </div>
            )}

            {transaction.refundReason && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Lý do hoàn tiền</p>
                <p className="text-blue-600">{transaction.refundReason}</p>
              </div>
            )}

            {transaction.cancellationReason && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Lý do hủy</p>
                <p className="text-gray-600">{transaction.cancellationReason}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* User Information */}
        {transaction.user && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Thông tin khách hàng
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Tên khách hàng</p>
                <p className="font-medium">{transaction.user.name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Email</p>
                <p>{transaction.user.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Số điện thoại</p>
                <p>{transaction.user.phone}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Vai trò</p>
                <Badge variant="outline">
                  {transaction.user.role === 'admin' ? 'Quản trị viên' : 'Khách hàng'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Order Information */}
      {transaction.order && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Thông tin đơn hàng
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Mã đơn hàng</p>
                <p className="font-mono">{transaction.order.id}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Trạng thái đơn hàng</p>
                <Badge variant="outline">{transaction.order.status}</Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Tổng tiền</p>
                <p className="font-semibold">{formatCurrency(transaction.order.totalAmount)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Ngày đặt</p>
                <p>{new Date(transaction.order.createdAt).toLocaleDateString("vi-VN")}</p>
              </div>
            </div>

            <Separator />

            {/* Order Items */}
            <div>
              <h4 className="font-medium mb-3">Sản phẩm đã đặt</h4>
              <div className="space-y-3">
                {transaction.order.items.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 p-3 border rounded-lg">
                    {item.productImage && (
                      <img
                        src={item.productImage}
                        alt={item.productName}
                        className="h-12 w-12 rounded-md object-cover"
                      />
                    )}
                    <div className="flex-1">
                      <p className="font-medium">{item.productName}</p>
                      <p className="text-sm text-muted-foreground">
                        Số lượng: {item.quantity} × {formatCurrency(item.price)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{formatCurrency(item.quantity * item.price)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Shipping Address */}
            {transaction.order.shippingAddress && (
              <>
                <Separator />
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Địa chỉ giao hàng
                  </h4>
                  <div className="p-3 border rounded-lg">
                    <p className="font-medium">{transaction.order.shippingAddress.fullName}</p>
                    <p className="text-sm text-muted-foreground">{transaction.order.shippingAddress.phone}</p>
                    <p className="text-sm">
                      {transaction.order.shippingAddress.address}, {transaction.order.shippingAddress.ward}, {transaction.order.shippingAddress.district}, {transaction.order.shippingAddress.city}
                    </p>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
