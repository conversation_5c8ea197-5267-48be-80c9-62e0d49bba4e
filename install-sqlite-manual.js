const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Manual better-sqlite3 installation script');
console.log('Current working directory:', process.cwd());

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('Error: package.json not found. Make sure you\'re in the project root directory.');
  process.exit(1);
}

console.log('✓ Found package.json');

// Check if better-sqlite3 is in package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
if (!packageJson.dependencies || !packageJson.dependencies['better-sqlite3']) {
  console.error('Error: better-sqlite3 not found in package.json dependencies');
  process.exit(1);
}

console.log('✓ better-sqlite3 found in package.json dependencies');

// Try different installation methods
const installMethods = [
  'npm install better-sqlite3',
  'npm install better-sqlite3 --force',
  'npm install better-sqlite3 --no-optional',
  'npm rebuild better-sqlite3'
];

for (const method of installMethods) {
  console.log(`\nTrying: ${method}`);
  try {
    execSync(method, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✓ Success with: ${method}`);
    
    // Test if it works
    try {
      const Database = require('better-sqlite3');
      console.log('✓ better-sqlite3 is now working!');
      process.exit(0);
    } catch (testError) {
      console.log('✗ Installation succeeded but module still not working:', testError.message);
    }
  } catch (error) {
    console.log(`✗ Failed with: ${method}`);
    console.log('Error:', error.message);
  }
}

console.log('\n❌ All installation methods failed');
console.log('You may need to:');
console.log('1. Install Visual Studio Build Tools');
console.log('2. Install Python');
console.log('3. Use a different SQLite library');
