import { NextResponse } from "next/server"
import { ProductModel, CategoryModel } from "../../../../lib/database/models"

// Extract status from description (temporary solution)
function extractStatusFromDescription(description: string): string {
  if (!description) return 'active'
  const match = description.match(/^\[(\w+)\]/)
  return match ? match[1] : 'active'
}

// Remove status prefix from description
function cleanDescription(description: string): string {
  if (!description) return ''
  return description.replace(/^\[\w+\]\s*/, '')
}

// Transform database product to API format
function transformProduct(dbProduct: any, categories: any[] = []) {
  // Find the category details
  const category = categories.find(c => c.id === dbProduct.category_id) || {
    id: dbProduct.category_id,
    name: dbProduct.category_id,
    slug: dbProduct.category_id
  }

  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: cleanDescription(dbProduct.description || ''),
    price: dbProduct.price,
    originalPrice: dbProduct.original_price,
    image: dbProduct.image,
    images: dbProduct.images ? JSON.parse(dbProduct.images) : [],
    category: {
      id: category.id,
      name: category.name,
      slug: category.slug
    },
    stock: dbProduct.stock,
    rating: dbProduct.rating,
    reviewCount: dbProduct.review_count,
    soldCount: dbProduct.sold_count,
    featured: Boolean(dbProduct.featured),
    isPromotional: Boolean(dbProduct.is_promotional),
    promotionEnds: dbProduct.promotion_ends,
    status: extractStatusFromDescription(dbProduct.description),
    createdAt: dbProduct.created_at
  }
}

// GET /api/admin/products
export async function GET() {
  try {
    // Get all products (both regular and promotional)
    const allProducts = ProductModel.getAll({
      orderBy: 'created_at',
      orderDirection: 'DESC'
    })

    // Get categories for proper transformation
    const categories = CategoryModel.getAll()

    // Transform products to API format
    const transformedProducts = allProducts.map(product => transformProduct(product, categories))

    return NextResponse.json(transformedProducts)
  } catch (error) {
    console.error("Error reading products:", error)
    return NextResponse.json(
      { error: "Failed to read products" },
      { status: 500 }
    )
  }
}

// POST /api/admin/products
export async function POST(request: Request) {
  try {
    const newProduct = await request.json()

    // Validate required fields
    if (!newProduct.name || !newProduct.price || !newProduct.categoryId || !newProduct.image) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Generate new product ID
    const existingProducts = ProductModel.getAll()
    const lastId = existingProducts.length > 0
      ? Math.max(...existingProducts.map(p => {
          const match = p.id.match(/\d+$/)
          return match ? parseInt(match[0]) : 0
        }))
      : 0
    const newId = `prod-${String(lastId + 1).padStart(3, "0")}`

    // Create new product object
    const productToAdd = {
      id: newId,
      name: newProduct.name,
      description: newProduct.description || "",
      price: newProduct.price,
      original_price: newProduct.originalPrice || null,
      image: newProduct.image,
      images: JSON.stringify(newProduct.images || [newProduct.image]),
      category_id: newProduct.categoryId,
      stock: newProduct.stock || 0,
      rating: 0,
      review_count: 0,
      sold_count: 0,
      featured: newProduct.featured || false,
      is_promotional: newProduct.isPromotional || false,
      promotion_ends: newProduct.promotionEnds || null
    }

    const createdProduct = ProductModel.create(productToAdd)
    const transformedProduct = transformProduct(createdProduct)
    return NextResponse.json(transformedProduct, { status: 201 })
  } catch (error) {
    console.error("Error creating product:", error)
    return NextResponse.json(
      { error: "Failed to create product" },
      { status: 500 }
    )
  }
}

