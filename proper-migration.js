// Proper migration using database utilities
const fs = require('fs');
const path = require('path');

// Import database utilities
const { getDatabase } = require('./lib/database/connection');
const { dbUtils } = require('./lib/database/utils');

console.log('🚀 Starting proper data migration...\n');

// Data file paths
const DATA_DIR = path.join(process.cwd(), 'data');
const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

// Helper function to read JSON file
function readJsonFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return [];
    }
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

// Migration functions
function migrateCategories() {
  console.log('Migrating categories...');
  const categories = readJsonFile(FILES.categories);
  
  let migrated = 0;
  for (const category of categories) {
    try {
      dbUtils.insert('categories', {
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        image: category.image,
        status: category.status || 'active',
        created_at: category.createdAt || new Date().toISOString()
      });
      migrated++;
    } catch (error) {
      console.error(`✗ Failed to migrate category ${category.id}:`, error.message);
    }
  }
  
  console.log(`✓ Migrated ${migrated}/${categories.length} categories`);
}

function migrateUsers() {
  console.log('Migrating users...');
  const users = readJsonFile(FILES.users);
  
  let migratedUsers = 0;
  let migratedAddresses = 0;
  
  for (const user of users) {
    try {
      // Create user
      dbUtils.insert('users', {
        id: user.id,
        name: user.name,
        email: user.email,
        password: user.password,
        phone: user.phone,
        role: user.role || 'customer',
        status: user.status || 'active',
        created_at: user.createdAt || new Date().toISOString()
      });
      migratedUsers++;
      
      // Create user address if exists
      if (user.address) {
        dbUtils.insert('user_addresses', {
          user_id: user.id,
          full_name: user.address.fullName,
          phone: user.address.phone,
          address: user.address.address,
          city: user.address.city,
          district: user.address.district,
          ward: user.address.ward,
          is_default: true,
          created_at: user.createdAt || new Date().toISOString()
        });
        migratedAddresses++;
      }
    } catch (error) {
      console.error(`✗ Failed to migrate user ${user.id}:`, error.message);
    }
  }
  
  console.log(`✓ Migrated ${migratedUsers}/${users.length} users and ${migratedAddresses} addresses`);
}

function migrateProducts() {
  console.log('Migrating products...');
  
  // Regular products
  const products = readJsonFile(FILES.products);
  // Promotional products
  const promotionalProducts = readJsonFile(FILES.promotionalProducts);
  
  let migrated = 0;
  
  // Migrate regular products
  for (const product of products) {
    try {
      dbUtils.insert('products', {
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        original_price: product.originalPrice,
        image: product.image,
        images: JSON.stringify(product.images || []),
        category_id: product.category?.id || product.category?.slug || 'unknown',
        stock: product.stock || 0,
        rating: product.rating || 0,
        review_count: product.reviewCount || 0,
        sold_count: product.soldCount || 0,
        featured: product.featured || false,
        is_promotional: false,
        promotion_ends: null,
        created_at: product.createdAt || new Date().toISOString()
      });
      migrated++;
    } catch (error) {
      console.error(`✗ Failed to migrate product ${product.id}:`, error.message);
    }
  }
  
  // Migrate promotional products
  for (const product of promotionalProducts) {
    try {
      dbUtils.insert('products', {
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        original_price: product.originalPrice,
        image: product.image,
        images: JSON.stringify(product.images || []),
        category_id: product.category?.id || product.category?.slug || 'unknown',
        stock: product.stock || 0,
        rating: product.rating || 0,
        review_count: product.reviewCount || 0,
        sold_count: product.soldCount || 0,
        featured: product.featured || false,
        is_promotional: true,
        promotion_ends: product.promotionEnds || null,
        created_at: product.createdAt || new Date().toISOString()
      });
      migrated++;
    } catch (error) {
      console.error(`✗ Failed to migrate promotional product ${product.id}:`, error.message);
    }
  }
  
  console.log(`✓ Migrated ${migrated}/${products.length + promotionalProducts.length} products`);
}

function migrateReviews() {
  console.log('Migrating reviews...');
  const reviews = readJsonFile(FILES.reviews);
  
  let migrated = 0;
  for (const review of reviews) {
    try {
      dbUtils.insert('reviews', {
        id: review.id,
        product_id: review.productId,
        user_id: review.userId,
        rating: review.rating,
        comment: review.comment,
        status: review.status || 'pending',
        created_at: review.createdAt || new Date().toISOString()
      });
      migrated++;
    } catch (error) {
      console.error(`✗ Failed to migrate review ${review.id}:`, error.message);
    }
  }
  
  console.log(`✓ Migrated ${migrated}/${reviews.length} reviews`);
}

function migrateOrders() {
  console.log('Migrating orders...');
  const orders = readJsonFile(FILES.orders);
  
  let migratedOrders = 0;
  let migratedItems = 0;
  let migratedAddresses = 0;
  
  for (const order of orders) {
    try {
      // Create order
      dbUtils.insert('orders', {
        id: order.id,
        user_id: order.userId,
        status: order.status || 'pending',
        total_amount: order.totalAmount,
        payment_method: order.paymentMethod,
        created_at: order.createdAt || new Date().toISOString()
      });
      migratedOrders++;
      
      // Create shipping address
      if (order.shippingAddress) {
        dbUtils.insert('order_shipping_addresses', {
          order_id: order.id,
          full_name: order.shippingAddress.fullName,
          phone: order.shippingAddress.phone,
          address: order.shippingAddress.address,
          city: order.shippingAddress.city,
          district: order.shippingAddress.district,
          ward: order.shippingAddress.ward
        });
        migratedAddresses++;
      }
      
      // Create order items
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          dbUtils.insert('order_items', {
            id: item.id,
            order_id: order.id,
            product_id: item.productId,
            quantity: item.quantity,
            price: item.price,
            product_name: item.product?.name || 'Unknown Product',
            product_image: item.product?.image || null
          });
          migratedItems++;
        }
      }
    } catch (error) {
      console.error(`✗ Failed to migrate order ${order.id}:`, error.message);
    }
  }
  
  console.log(`✓ Migrated ${migratedOrders}/${orders.length} orders, ${migratedItems} items, ${migratedAddresses} addresses`);
}

function migrateTransactions() {
  console.log('Migrating transactions...');
  const transactions = readJsonFile(FILES.transactions);
  
  let migrated = 0;
  for (const transaction of transactions) {
    try {
      dbUtils.insert('transactions', {
        id: transaction.id,
        order_id: transaction.orderId,
        user_id: transaction.userId,
        amount: transaction.amount,
        status: transaction.status || 'pending',
        payment_method: transaction.paymentMethod,
        created_at: transaction.createdAt || new Date().toISOString()
      });
      migrated++;
    } catch (error) {
      console.error(`✗ Failed to migrate transaction ${transaction.id}:`, error.message);
    }
  }
  
  console.log(`✓ Migrated ${migrated}/${transactions.length} transactions`);
}

// Main migration function
async function runMigration() {
  try {
    console.log('Initializing database connection...');
    const db = getDatabase();
    console.log('✓ Database connection established');
    console.log('Database type:', db._isFileBased ? 'File-based' : 'SQLite');
    
    // Run migrations in order (respecting foreign key constraints)
    migrateCategories();
    migrateUsers();
    migrateProducts();
    migrateReviews();
    migrateOrders();
    migrateTransactions();
    
    console.log('\n🎉 Migration completed successfully!');
    
    // Print summary
    console.log('\n📊 Migration Summary:');
    console.log(`   Categories: ${dbUtils.count('categories')}`);
    console.log(`   Users: ${dbUtils.count('users')}`);
    console.log(`   User Addresses: ${dbUtils.count('user_addresses')}`);
    console.log(`   Products: ${dbUtils.count('products')}`);
    console.log(`   Reviews: ${dbUtils.count('reviews')}`);
    console.log(`   Orders: ${dbUtils.count('orders')}`);
    console.log(`   Order Items: ${dbUtils.count('order_items')}`);
    console.log(`   Order Shipping Addresses: ${dbUtils.count('order_shipping_addresses')}`);
    console.log(`   Transactions: ${dbUtils.count('transactions')}`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Run migration
runMigration();
