// Simple admin test
const fs = require('fs');
const path = require('path');

console.log('🧪 Simple admin API test...\n');

try {
  // Read the database directly
  const dbPath = path.join(process.cwd(), 'data', 'store.json');
  console.log('Reading database from:', dbPath);
  
  if (!fs.existsSync(dbPath)) {
    console.error('❌ Database file not found!');
    process.exit(1);
  }
  
  const dbData = JSON.parse(fs.readFileSync(dbPath, 'utf-8'));
  
  console.log('✓ Database loaded successfully');
  console.log(`   Products: ${dbData.products.length}`);
  console.log(`   Categories: ${dbData.categories.length}`);
  
  // Test finding prod-037
  const prod037 = dbData.products.find(p => p.id === 'prod-037');
  if (prod037) {
    console.log('\n✓ Found prod-037:');
    console.log(`   Name: ${prod037.name}`);
    console.log(`   Price: ${prod037.price.toLocaleString()} VND`);
    console.log(`   Stock: ${prod037.stock}`);
    console.log(`   Category ID: ${prod037.category_id}`);
    
    // Find the category
    const category = dbData.categories.find(c => c.id === prod037.category_id);
    if (category) {
      console.log(`   Category Name: ${category.name}`);
    }
  } else {
    console.log('✗ Product prod-037 not found');
    console.log('Available product IDs:', dbData.products.slice(0, 5).map(p => p.id));
  }
  
  // Test status extraction
  console.log('\n🔍 Testing status extraction...');
  const testDescriptions = [
    'Normal product description',
    '[pending] Product pending approval',
    '[hidden] Hidden product',
    '[active] Active product'
  ];
  
  testDescriptions.forEach(desc => {
    const match = desc.match(/^\[(\w+)\]/);
    const status = match ? match[1] : 'active';
    const cleanDesc = desc.replace(/^\[\w+\]\s*/, '');
    console.log(`   "${desc}" -> status: "${status}", clean: "${cleanDesc}"`);
  });
  
  console.log('\n🎉 Simple admin test completed successfully!');

} catch (error) {
  console.error('❌ Simple admin test failed:', error.message);
}
