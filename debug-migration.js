// Debug migration script
const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging migration...\n');

// Check current directory
console.log('Current working directory:', process.cwd());

// Data file paths
const DATA_DIR = path.join(process.cwd(), 'data');
console.log('Data directory:', DATA_DIR);

const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

console.log('\n📁 Checking data files:');
Object.entries(FILES).forEach(([name, filePath]) => {
  const exists = fs.existsSync(filePath);
  console.log(`  ${name}: ${exists ? '✓' : '✗'} ${filePath}`);
  
  if (exists) {
    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
      console.log(`    Records: ${Array.isArray(data) ? data.length : 'Not an array'}`);
    } catch (error) {
      console.log(`    Error reading: ${error.message}`);
    }
  }
});

// Test reading categories specifically
console.log('\n📋 Testing categories file:');
try {
  const categoriesPath = FILES.categories;
  if (fs.existsSync(categoriesPath)) {
    const categoriesData = fs.readFileSync(categoriesPath, 'utf-8');
    console.log('Raw categories data (first 200 chars):', categoriesData.substring(0, 200));
    
    const categories = JSON.parse(categoriesData);
    console.log('Parsed categories:', categories.length, 'items');
    if (categories.length > 0) {
      console.log('First category:', JSON.stringify(categories[0], null, 2));
    }
  } else {
    console.log('Categories file does not exist');
  }
} catch (error) {
  console.error('Error testing categories:', error.message);
}
