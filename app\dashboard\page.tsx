"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Package } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ProductCard } from "@/components/product-card"
import { useCart } from "@/hooks/use-cart"
import { useWishlist } from "@/hooks/use-wishlist"
import { useComparison } from "@/hooks/use-comparison"
import type { Product } from "@/lib/types"
import DashboardLayout from "./layout"

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

interface Category {
  id: string
  name: string
  slug: string
}

interface DashboardProduct {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  image: string
  images?: string[]
  category: Category
  stock: number
  rating: number
  reviewCount: number
  soldCount: number
  featured: boolean
  promotional?: boolean
  createdAt: string
}

export default function DashboardPage() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [products, setProducts] = useState<DashboardProduct[]>([])
  const [loading, setLoading] = useState(true)

  // Initialize hooks for enhanced functionality
  const { addToCart } = useCart()
  const { toggleWishlist, isInWishlist } = useWishlist()
  const { toggleComparison } = useComparison()

  // Handler functions for product card interactions
  const handleAddToCart = async (product: Product, quantity: number = 1) => {
    return await addToCart(product, quantity)
  }

  const handleToggleWishlist = async (product: Product, isWishlisted: boolean) => {
    return await toggleWishlist(product)
  }

  const handleShare = async (product: Product) => {
    const url = `${window.location.origin}/product/${product.id}`
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: product.description,
          url: url,
        })
      } catch (error) {
        console.error('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(url)
    }
  }

  const handleCompare = async (product: Product) => {
    return await toggleComparison(product)
  }

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      setUser(JSON.parse(userData))
    } else {
      router.push("/login")
    }
  }, [router])

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch("/api/products")
        const data = await response.json()
        setProducts(data)
      } catch (error) {
        console.error("Error fetching products:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  const featuredProducts = products.filter((p) => p.featured)
  const newArrivals = products.slice(0, 4)
  const promotionalProducts = products.filter((p) => p.promotional)

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <section className="w-full">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                  Xin chào, {user?.name || "User"}!
                </h1>
                <p className="max-w-[600px] text-muted-foreground md:text-xl">
                  Chào mừng bạn đến với TechStore. Khám phá các sản phẩm công nghệ mới nhất.
                </p>
              </div>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Button asChild>
                  <Link href="/products">Xem sản phẩm</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/orders">Xem đơn hàng</Link>
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <img
                src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=1472&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                alt="Hero"
                className="aspect-video overflow-hidden rounded-xl object-cover object-center"
                width="600"
                height="400"
              />
            </div>
          </div>
        </section>

        <section className="w-full py-12 bg-muted rounded-lg">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Thông tin cá nhân</CardTitle>
                <CardDescription>Thông tin cơ bản của bạn</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><strong>Họ tên:</strong> {user?.name || "User"}</p>
                  <p><strong>Email:</strong> {user?.email || "Chưa cập nhật"}</p>
                  <p><strong>Số điện thoại:</strong> {user?.phone || "Chưa cập nhật"}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Địa chỉ giao hàng</CardTitle>
                <CardDescription>Địa chỉ mặc định của bạn</CardDescription>
              </CardHeader>
              <CardContent>
                {user?.address ? (
                  <div className="space-y-2">
                    <p><strong>Người nhận:</strong> {user.address.fullName}</p>
                    <p><strong>Số điện thoại:</strong> {user.address.phone}</p>
                    <p><strong>Địa chỉ:</strong> {user.address.address}</p>
                    <p><strong>Quận/Huyện:</strong> {user.address.district}</p>
                    <p><strong>Phường/Xã:</strong> {user.address.ward}</p>
                    <p><strong>Tỉnh/Thành phố:</strong> {user.address.city}</p>
                  </div>
                ) : (
                  <p>Bạn chưa cập nhật địa chỉ giao hàng</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quản lý tài khoản</CardTitle>
                <CardDescription>Các tùy chọn quản lý</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/profile">Cập nhật thông tin</Link>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/orders">Xem đơn hàng</Link>
                  </Button>
                  {user?.role === "admin" && (
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/admin">Quản trị hệ thống</Link>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {featuredProducts.length > 0 && (
          <section className="w-full">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">Sản phẩm nổi bật</h2>
              <Button variant="ghost" asChild>
                <Link href="/products?featured=true">Xem tất cả</Link>
              </Button>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {featuredProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={{
                    id: product.id,
                    name: product.name,
                    description: product.description,
                    price: product.price,
                    originalPrice: product.originalPrice,
                    image: product.image,
                    images: product.images,
                    category: product.category,
                    stock: product.stock,
                    rating: product.rating,
                    reviewCount: product.reviewCount,
                    soldCount: product.soldCount,
                    featured: product.featured,
                    promotional: product.promotional,
                    createdAt: new Date(product.createdAt),
                  }}
                  onAddToCart={handleAddToCart}
                  onToggleWishlist={handleToggleWishlist}
                  onShare={handleShare}
                  onCompare={handleCompare}
                />
              ))}
            </div>
          </section>
        )}

        {newArrivals.length > 0 && (
          <section className="w-full">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">Sản phẩm mới</h2>
              <Button variant="ghost" asChild>
                <Link href="/products?sort=newest">Xem tất cả</Link>
              </Button>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {newArrivals.map((product) => (
                <ProductCard
                  key={product.id}
                  product={{
                    id: product.id,
                    name: product.name,
                    description: product.description,
                    price: product.price,
                    originalPrice: product.originalPrice,
                    image: product.image,
                    images: product.images,
                    category: product.category,
                    stock: product.stock,
                    rating: product.rating,
                    reviewCount: product.reviewCount,
                    soldCount: product.soldCount,
                    featured: product.featured,
                    promotional: product.promotional,
                    createdAt: new Date(product.createdAt),
                  }}
                  onAddToCart={handleAddToCart}
                  onToggleWishlist={handleToggleWishlist}
                  onShare={handleShare}
                  onCompare={handleCompare}
                />
              ))}
            </div>
          </section>
        )}

        {promotionalProducts.length > 0 && (
          <section className="w-full">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">Sản phẩm khuyến mãi</h2>
              <Button variant="ghost" asChild>
                <Link href="/products?promotional=true">Xem tất cả</Link>
              </Button>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {promotionalProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={{
                    id: product.id,
                    name: product.name,
                    description: product.description,
                    price: product.price,
                    originalPrice: product.originalPrice,
                    image: product.image,
                    images: product.images,
                    category: product.category,
                    stock: product.stock,
                    rating: product.rating,
                    reviewCount: product.reviewCount,
                    soldCount: product.soldCount,
                    featured: product.featured,
                    promotional: product.promotional,
                    createdAt: new Date(product.createdAt),
                  }}
                  onAddToCart={handleAddToCart}
                  onToggleWishlist={handleToggleWishlist}
                  onShare={handleShare}
                  onCompare={handleCompare}
                />
              ))}
            </div>
          </section>
        )}
      </div>
    </DashboardLayout>
  )
} 