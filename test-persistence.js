// Test data persistence across application restarts
const fs = require('fs');
const path = require('path');

console.log('🔄 Testing data persistence across application restarts...\n');

const dbPath = path.join(process.cwd(), 'data', 'store.json');
const backupPath = path.join(process.cwd(), 'data', 'store-backup.json');

try {
  // Step 1: Create backup of current database
  console.log('1. Creating backup of current database...');
  if (fs.existsSync(dbPath)) {
    fs.copyFileSync(dbPath, backupPath);
    console.log('✓ Backup created');
  } else {
    throw new Error('Database file not found!');
  }

  // Step 2: Read initial state
  console.log('\n2. Reading initial database state...');
  let db = JSON.parse(fs.readFileSync(dbPath, 'utf-8'));
  const initialState = {
    categories: db.categories.length,
    users: db.users.length,
    products: db.products.length,
    orders: db.orders.length,
    lastMigration: db._meta.lastMigration
  };
  console.log('✓ Initial state recorded:');
  console.log(`   Categories: ${initialState.categories}`);
  console.log(`   Users: ${initialState.users}`);
  console.log(`   Products: ${initialState.products}`);
  console.log(`   Orders: ${initialState.orders}`);
  console.log(`   Last Migration: ${initialState.lastMigration}`);

  // Step 3: Add test data to simulate application activity
  console.log('\n3. Adding test data to simulate application activity...');
  const testData = {
    category: {
      id: `persist-test-cat-${Date.now()}`,
      name: 'Persistence Test Category',
      slug: 'persistence-test-category',
      description: 'Category created to test persistence',
      status: 'active',
      created_at: new Date().toISOString()
    },
    user: {
      id: `persist-test-user-${Date.now()}`,
      name: 'Persistence Test User',
      email: `persisttest${Date.now()}@example.com`,
      password: 'hashedpassword',
      role: 'customer',
      status: 'active',
      created_at: new Date().toISOString()
    }
  };

  db.categories.push(testData.category);
  db.users.push(testData.user);
  db._meta.lastActivity = new Date().toISOString();

  // Save the modified database
  fs.writeFileSync(dbPath, JSON.stringify(db, null, 2));
  console.log('✓ Test data added and saved');
  console.log(`   Added category: ${testData.category.name}`);
  console.log(`   Added user: ${testData.user.name}`);

  // Step 4: Simulate application restart by clearing memory and reloading
  console.log('\n4. Simulating application restart...');
  db = null; // Clear from memory
  console.log('✓ Memory cleared (simulating app shutdown)');

  // Reload database from file (simulating app startup)
  db = JSON.parse(fs.readFileSync(dbPath, 'utf-8'));
  console.log('✓ Database reloaded from file (simulating app startup)');

  // Step 5: Verify data persistence
  console.log('\n5. Verifying data persistence after restart...');
  
  const afterRestartState = {
    categories: db.categories.length,
    users: db.users.length,
    products: db.products.length,
    orders: db.orders.length,
    lastMigration: db._meta.lastMigration,
    lastActivity: db._meta.lastActivity
  };

  console.log('✓ Post-restart state:');
  console.log(`   Categories: ${afterRestartState.categories}`);
  console.log(`   Users: ${afterRestartState.users}`);
  console.log(`   Products: ${afterRestartState.products}`);
  console.log(`   Orders: ${afterRestartState.orders}`);

  // Verify test data still exists
  const persistedCategory = db.categories.find(c => c.id === testData.category.id);
  const persistedUser = db.users.find(u => u.id === testData.user.id);

  if (persistedCategory) {
    console.log('✓ Test category persisted:', persistedCategory.name);
  } else {
    throw new Error('Test category did not persist!');
  }

  if (persistedUser) {
    console.log('✓ Test user persisted:', persistedUser.name);
  } else {
    throw new Error('Test user did not persist!');
  }

  // Verify counts increased correctly
  if (afterRestartState.categories === initialState.categories + 1 &&
      afterRestartState.users === initialState.users + 1) {
    console.log('✓ Data counts are correct after restart');
  } else {
    throw new Error('Data counts are incorrect after restart!');
  }

  // Verify metadata persisted
  if (afterRestartState.lastMigration === initialState.lastMigration) {
    console.log('✓ Migration metadata persisted');
  } else {
    throw new Error('Migration metadata did not persist!');
  }

  if (afterRestartState.lastActivity) {
    console.log('✓ Activity metadata persisted');
  } else {
    throw new Error('Activity metadata did not persist!');
  }

  // Step 6: Test multiple restart cycles
  console.log('\n6. Testing multiple restart cycles...');
  
  for (let i = 1; i <= 3; i++) {
    console.log(`   Cycle ${i}:`);
    
    // Add more data
    const cycleData = {
      id: `cycle-${i}-${Date.now()}`,
      name: `Cycle ${i} Test Product`,
      description: `Product created in restart cycle ${i}`,
      price: 100000 * i,
      category_id: testData.category.id,
      stock: 10,
      rating: 0,
      review_count: 0,
      sold_count: 0,
      featured: false,
      is_promotional: false,
      created_at: new Date().toISOString()
    };
    
    db.products.push(cycleData);
    fs.writeFileSync(dbPath, JSON.stringify(db, null, 2));
    console.log(`     Added product: ${cycleData.name}`);
    
    // Simulate restart
    db = null;
    db = JSON.parse(fs.readFileSync(dbPath, 'utf-8'));
    
    // Verify data
    const cycleProduct = db.products.find(p => p.id === cycleData.id);
    if (cycleProduct) {
      console.log(`     ✓ Product persisted after cycle ${i}`);
    } else {
      throw new Error(`Product did not persist after cycle ${i}!`);
    }
  }

  // Step 7: Clean up test data
  console.log('\n7. Cleaning up test data...');
  
  // Remove test data
  db.categories = db.categories.filter(c => !c.id.includes('persist-test'));
  db.users = db.users.filter(u => !u.id.includes('persist-test'));
  db.products = db.products.filter(p => !p.id.includes('cycle-'));
  delete db._meta.lastActivity;
  
  // Save cleaned database
  fs.writeFileSync(dbPath, JSON.stringify(db, null, 2));
  console.log('✓ Test data cleaned up');

  // Verify we're back to initial state
  const finalState = {
    categories: db.categories.length,
    users: db.users.length,
    products: db.products.length,
    orders: db.orders.length
  };

  if (finalState.categories === initialState.categories &&
      finalState.users === initialState.users &&
      finalState.products === initialState.products &&
      finalState.orders === initialState.orders) {
    console.log('✓ Database restored to initial state');
  } else {
    console.log('⚠️ Database state differs from initial:');
    console.log(`   Categories: ${initialState.categories} → ${finalState.categories}`);
    console.log(`   Users: ${initialState.users} → ${finalState.users}`);
    console.log(`   Products: ${initialState.products} → ${finalState.products}`);
    console.log(`   Orders: ${initialState.orders} → ${finalState.orders}`);
  }

  // Step 8: Remove backup
  console.log('\n8. Removing backup...');
  if (fs.existsSync(backupPath)) {
    fs.unlinkSync(backupPath);
    console.log('✓ Backup removed');
  }

  console.log('\n🎉 Data persistence test completed successfully!');
  console.log('✅ Data persists correctly across application restarts');
  console.log('✅ Multiple restart cycles work correctly');
  console.log('✅ File-based database provides reliable persistence');

} catch (error) {
  console.error('❌ Persistence test failed:', error.message);
  
  // Restore from backup if it exists
  if (fs.existsSync(backupPath)) {
    console.log('🔄 Restoring from backup...');
    fs.copyFileSync(backupPath, dbPath);
    fs.unlinkSync(backupPath);
    console.log('✓ Database restored from backup');
  }
  
  console.error('Stack:', error.stack);
  process.exit(1);
}
