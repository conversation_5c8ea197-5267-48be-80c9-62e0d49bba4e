// Migration script for file-based database
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting data migration to file-based database...\n');

// Data file paths
const DATA_DIR = path.join(process.cwd(), 'data');
const FILES = {
  categories: path.join(DATA_DIR, 'categories.json'),
  users: path.join(DATA_DIR, 'users.json'),
  products: path.join(DATA_DIR, 'products.json'),
  promotionalProducts: path.join(DATA_DIR, 'promotionalProducts.json'),
  reviews: path.join(DATA_DIR, 'reviews.json'),
  orders: path.join(DATA_DIR, 'orders.json'),
  transactions: path.join(DATA_DIR, 'transactions.json')
};

const DB_FILE = path.join(DATA_DIR, 'store.json');

// Helper function to read JSON file
function readJsonFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return [];
    }
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

// Load existing database or create new one
function loadDatabase() {
  if (fs.existsSync(DB_FILE)) {
    try {
      const data = fs.readFileSync(DB_FILE, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error loading database file:', error.message);
    }
  }
  
  // Create initial structure
  return {
    categories: [],
    users: [],
    user_addresses: [],
    products: [],
    reviews: [],
    orders: [],
    order_items: [],
    order_shipping_addresses: [],
    transactions: [],
    _meta: {
      created: new Date().toISOString(),
      version: '1.0.0',
      lastMigration: null
    }
  };
}

// Save database
function saveDatabase(data) {
  try {
    fs.writeFileSync(DB_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error saving database file:', error.message);
    throw error;
  }
}

// Migration functions
function migrateCategories(db) {
  console.log('Migrating categories...');
  const categories = readJsonFile(FILES.categories);
  
  db.categories = categories.map(category => ({
    id: category.id,
    name: category.name,
    slug: category.slug,
    description: category.description,
    image: category.image,
    status: category.status || 'active',
    created_at: category.createdAt || new Date().toISOString()
  }));
  
  console.log(`✓ Migrated ${db.categories.length} categories`);
}

function migrateUsers(db) {
  console.log('Migrating users...');
  const users = readJsonFile(FILES.users);
  
  db.users = [];
  db.user_addresses = [];
  
  users.forEach(user => {
    // Add user
    db.users.push({
      id: user.id,
      name: user.name,
      email: user.email,
      password: user.password,
      phone: user.phone,
      role: user.role || 'customer',
      status: user.status || 'active',
      created_at: user.createdAt || new Date().toISOString()
    });
    
    // Add user address if exists
    if (user.address) {
      db.user_addresses.push({
        id: db.user_addresses.length + 1,
        user_id: user.id,
        full_name: user.address.fullName,
        phone: user.address.phone,
        address: user.address.address,
        city: user.address.city,
        district: user.address.district,
        ward: user.address.ward,
        is_default: true,
        created_at: user.createdAt || new Date().toISOString()
      });
    }
  });
  
  console.log(`✓ Migrated ${db.users.length} users and ${db.user_addresses.length} addresses`);
}

function migrateProducts(db) {
  console.log('Migrating products...');
  
  // Regular products
  const products = readJsonFile(FILES.products);
  // Promotional products
  const promotionalProducts = readJsonFile(FILES.promotionalProducts);
  
  db.products = [];
  
  // Migrate regular products
  products.forEach(product => {
    db.products.push({
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      original_price: product.originalPrice,
      image: product.image,
      images: JSON.stringify(product.images || []),
      category_id: product.category?.id || product.category?.slug || 'unknown',
      stock: product.stock || 0,
      rating: product.rating || 0,
      review_count: product.reviewCount || 0,
      sold_count: product.soldCount || 0,
      featured: product.featured || false,
      is_promotional: false,
      promotion_ends: null,
      created_at: product.createdAt || new Date().toISOString()
    });
  });
  
  // Migrate promotional products
  promotionalProducts.forEach(product => {
    db.products.push({
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      original_price: product.originalPrice,
      image: product.image,
      images: JSON.stringify(product.images || []),
      category_id: product.category?.id || product.category?.slug || 'unknown',
      stock: product.stock || 0,
      rating: product.rating || 0,
      review_count: product.reviewCount || 0,
      sold_count: product.soldCount || 0,
      featured: product.featured || false,
      is_promotional: true,
      promotion_ends: product.promotionEnds || null,
      created_at: product.createdAt || new Date().toISOString()
    });
  });
  
  console.log(`✓ Migrated ${db.products.length} products (${products.length} regular + ${promotionalProducts.length} promotional)`);
}

function migrateReviews(db) {
  console.log('Migrating reviews...');
  const reviews = readJsonFile(FILES.reviews);
  
  db.reviews = reviews.map(review => ({
    id: review.id,
    product_id: review.productId,
    user_id: review.userId,
    rating: review.rating,
    comment: review.comment,
    status: review.status || 'pending',
    created_at: review.createdAt || new Date().toISOString()
  }));
  
  console.log(`✓ Migrated ${db.reviews.length} reviews`);
}

function migrateOrders(db) {
  console.log('Migrating orders...');
  const orders = readJsonFile(FILES.orders);
  
  db.orders = [];
  db.order_items = [];
  db.order_shipping_addresses = [];
  
  orders.forEach(order => {
    // Add order
    db.orders.push({
      id: order.id,
      user_id: order.userId,
      status: order.status || 'pending',
      total_amount: order.totalAmount,
      payment_method: order.paymentMethod,
      created_at: order.createdAt || new Date().toISOString()
    });
    
    // Add shipping address
    if (order.shippingAddress) {
      db.order_shipping_addresses.push({
        id: db.order_shipping_addresses.length + 1,
        order_id: order.id,
        full_name: order.shippingAddress.fullName,
        phone: order.shippingAddress.phone,
        address: order.shippingAddress.address,
        city: order.shippingAddress.city,
        district: order.shippingAddress.district,
        ward: order.shippingAddress.ward
      });
    }
    
    // Add order items
    if (order.items && Array.isArray(order.items)) {
      order.items.forEach(item => {
        db.order_items.push({
          id: item.id,
          order_id: order.id,
          product_id: item.productId,
          quantity: item.quantity,
          price: item.price,
          product_name: item.product?.name || 'Unknown Product',
          product_image: item.product?.image || null
        });
      });
    }
  });
  
  console.log(`✓ Migrated ${db.orders.length} orders, ${db.order_items.length} items, ${db.order_shipping_addresses.length} addresses`);
}

function migrateTransactions(db) {
  console.log('Migrating transactions...');
  const transactions = readJsonFile(FILES.transactions);
  
  db.transactions = transactions.map(transaction => ({
    id: transaction.id,
    order_id: transaction.orderId,
    user_id: transaction.userId,
    amount: transaction.amount,
    status: transaction.status || 'pending',
    payment_method: transaction.paymentMethod,
    created_at: transaction.createdAt || new Date().toISOString()
  }));
  
  console.log(`✓ Migrated ${db.transactions.length} transactions`);
}

// Main migration function
async function runMigration() {
  try {
    console.log('Loading database...');
    const db = loadDatabase();
    
    // Clear existing data
    console.log('Clearing existing data...');
    db.categories = [];
    db.users = [];
    db.user_addresses = [];
    db.products = [];
    db.reviews = [];
    db.orders = [];
    db.order_items = [];
    db.order_shipping_addresses = [];
    db.transactions = [];
    
    // Run migrations
    migrateCategories(db);
    migrateUsers(db);
    migrateProducts(db);
    migrateReviews(db);
    migrateOrders(db);
    migrateTransactions(db);
    
    // Update metadata
    db._meta.lastMigration = new Date().toISOString();
    
    // Save database
    console.log('\nSaving database...');
    saveDatabase(db);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Migration Summary:');
    console.log(`   Categories: ${db.categories.length}`);
    console.log(`   Users: ${db.users.length}`);
    console.log(`   User Addresses: ${db.user_addresses.length}`);
    console.log(`   Products: ${db.products.length}`);
    console.log(`   Reviews: ${db.reviews.length}`);
    console.log(`   Orders: ${db.orders.length}`);
    console.log(`   Order Items: ${db.order_items.length}`);
    console.log(`   Order Shipping Addresses: ${db.order_shipping_addresses.length}`);
    console.log(`   Transactions: ${db.transactions.length}`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration
runMigration();
