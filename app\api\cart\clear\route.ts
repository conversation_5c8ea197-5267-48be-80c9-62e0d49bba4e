import { NextResponse } from "next/server"
import { CartModel } from "../../../../lib/database/models"

// DELETE /api/cart/clear - Clear all items from user's cart
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Clear all cart items for the user
    const deleted = CartModel.deleteByUserId(userId)
    
    if (!deleted) {
      return NextResponse.json(
        { error: 'Failed to clear cart' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Cart cleared successfully',
      totalItems: 0
    })
  } catch (error) {
    console.error('Error clearing cart:', error)
    return NextResponse.json(
      { error: 'Failed to clear cart' },
      { status: 500 }
    )
  }
}
