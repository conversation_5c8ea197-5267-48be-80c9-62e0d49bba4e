import { NextResponse } from "next/server"
import { ProductModel, CategoryModel } from "../../../../../lib/database/models"

// Transform database product to API format with category details
function transformProduct(dbProduct: any, categories: any[] = []) {
  // Find the category details
  const category = categories.find(c => c.id === dbProduct.category_id) || {
    id: dbProduct.category_id,
    name: dbProduct.category_id,
    slug: dbProduct.category_id
  }

  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description,
    price: dbProduct.price,
    originalPrice: dbProduct.original_price,
    image: dbProduct.image,
    images: dbProduct.images ? JSON.parse(dbProduct.images) : [],
    category: {
      id: category.id,
      name: category.name,
      slug: category.slug
    },
    stock: dbProduct.stock,
    rating: dbProduct.rating,
    reviewCount: dbProduct.review_count,
    soldCount: dbProduct.sold_count,
    featured: <PERSON><PERSON><PERSON>(dbProduct.featured),
    isPromotional: <PERSON><PERSON><PERSON>(dbProduct.is_promotional),
    promotionEnds: dbProduct.promotion_ends,
    status: extractStatusFromDescription(dbProduct.description) || 'active',
    createdAt: dbProduct.created_at
  }
}

// Extract status from description (temporary solution)
function extractStatusFromDescription(description: string): string | null {
  if (!description) return null
  const match = description.match(/^\[(\w+)\]/)
  return match ? match[1] : null
}

// Remove status prefix from description
function cleanDescription(description: string): string {
  if (!description) return ''
  return description.replace(/^\[\w+\]\s*/, '')
}

// GET /api/admin/products/[id]
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const product = ProductModel.getById(id)

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    // Get categories for proper transformation
    const categories = CategoryModel.getAll()
    const transformedProduct = transformProduct(product, categories)

    return NextResponse.json(transformedProduct)
  } catch (error) {
    console.error("Error fetching product:", error)
    return NextResponse.json(
      { error: "Failed to fetch product" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/products/[id]
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const updateData = await request.json()

    // Check if product exists
    const existingProduct = ProductModel.getById(id)
    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    // Transform API data to database format
    const dbUpdateData: any = {}

    // Handle status update (store in description temporarily)
    if (updateData.status !== undefined) {
      const currentDescription = cleanDescription(existingProduct.description || '')
      if (updateData.status === 'active') {
        dbUpdateData.description = currentDescription
      } else {
        dbUpdateData.description = `[${updateData.status}] ${currentDescription}`
      }
    }

    // Handle other field updates
    if (updateData.name !== undefined) dbUpdateData.name = updateData.name
    if (updateData.description !== undefined && updateData.status === undefined) {
      // Only update description if status is not being changed
      dbUpdateData.description = updateData.description
    }
    if (updateData.price !== undefined) dbUpdateData.price = updateData.price
    if (updateData.originalPrice !== undefined) dbUpdateData.original_price = updateData.originalPrice
    if (updateData.image !== undefined) dbUpdateData.image = updateData.image
    if (updateData.images !== undefined) dbUpdateData.images = JSON.stringify(updateData.images)
    if (updateData.categoryId !== undefined) dbUpdateData.category_id = updateData.categoryId
    if (updateData.stock !== undefined) dbUpdateData.stock = updateData.stock
    if (updateData.featured !== undefined) dbUpdateData.featured = updateData.featured
    if (updateData.isPromotional !== undefined) dbUpdateData.is_promotional = updateData.isPromotional
    if (updateData.promotionEnds !== undefined) dbUpdateData.promotion_ends = updateData.promotionEnds

    const updatedProduct = ProductModel.update(id, dbUpdateData)

    if (!updatedProduct) {
      return NextResponse.json(
        { error: "Failed to update product" },
        { status: 500 }
      )
    }

    // Get categories for proper transformation
    const categories = CategoryModel.getAll()
    const transformedProduct = transformProduct(updatedProduct, categories)

    return NextResponse.json(transformedProduct)
  } catch (error) {
    console.error("Error updating product:", error)
    return NextResponse.json(
      { error: "Failed to update product" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/products/[id]
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Check if product exists
    const existingProduct = ProductModel.getById(id)
    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    const deleted = ProductModel.delete(id)

    if (!deleted) {
      return NextResponse.json(
        { error: "Failed to delete product" },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: "Product deleted successfully",
      deletedProduct: {
        id: existingProduct.id,
        name: existingProduct.name
      }
    })
  } catch (error) {
    console.error("Error deleting product:", error)
    return NextResponse.json(
      { error: "Failed to delete product" },
      { status: 500 }
    )
  }
}
