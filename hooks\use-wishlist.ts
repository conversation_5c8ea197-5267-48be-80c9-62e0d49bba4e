"use client"

import { useState, useEffect } from "react"
import { toast } from "@/components/ui/use-toast"
import type { Product } from "@/lib/types"

interface WishlistState {
  items: Product[]
  isLoading: boolean
}

export function useWishlist() {
  const [wishlist, setWishlist] = useState<WishlistState>({
    items: [],
    isLoading: false,
  })

  // Get user from localStorage
  const getUser = () => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem("user")
      return userData ? JSON.parse(userData) : null
    }
    return null
  }

  // Load wishlist from localStorage (fallback until API is implemented)
  const loadWishlist = () => {
    const user = getUser()
    if (!user) return

    if (typeof window !== 'undefined') {
      const wishlistData = localStorage.getItem(`wishlist_${user.id}`)
      if (wishlistData) {
        try {
          const items = JSON.parse(wishlistData)
          setWishlist({ items, isLoading: false })
        } catch (error) {
          console.error("Error parsing wishlist data:", error)
          setWishlist({ items: [], isLoading: false })
        }
      }
    }
  }

  // Save wishlist to localStorage (fallback until API is implemented)
  const saveWishlist = (items: Product[]) => {
    const user = getUser()
    if (!user) return

    if (typeof window !== 'undefined') {
      localStorage.setItem(`wishlist_${user.id}`, JSON.stringify(items))
    }
  }

  // Check if product is in wishlist
  const isInWishlist = (productId: string): boolean => {
    return wishlist.items.some(item => item.id === productId)
  }

  // Add product to wishlist
  const addToWishlist = async (product: Product) => {
    const user = getUser()
    if (!user) {
      toast({
        title: "Yêu cầu đăng nhập",
        description: "Vui lòng đăng nhập để thêm sản phẩm vào danh sách yêu thích.",
        variant: "destructive",
      })
      return false
    }

    if (isInWishlist(product.id)) {
      toast({
        title: "Sản phẩm đã có trong danh sách",
        description: "Sản phẩm này đã có trong danh sách yêu thích của bạn.",
        variant: "destructive",
      })
      return false
    }

    try {
      const newItems = [...wishlist.items, product]
      setWishlist({ items: newItems, isLoading: false })
      saveWishlist(newItems)
      
      toast({
        title: "Đã thêm vào danh sách yêu thích",
        description: `${product.name} đã được thêm vào danh sách yêu thích của bạn.`,
      })
      return true
    } catch (error) {
      console.error("Error adding to wishlist:", error)
      toast({
        title: "Lỗi",
        description: "Không thể thêm sản phẩm vào danh sách yêu thích.",
        variant: "destructive",
      })
      return false
    }
  }

  // Remove product from wishlist
  const removeFromWishlist = async (productId: string) => {
    const user = getUser()
    if (!user) return false

    try {
      const newItems = wishlist.items.filter(item => item.id !== productId)
      setWishlist({ items: newItems, isLoading: false })
      saveWishlist(newItems)
      
      const removedProduct = wishlist.items.find(item => item.id === productId)
      toast({
        title: "Đã xóa khỏi danh sách yêu thích",
        description: `${removedProduct?.name || "Sản phẩm"} đã được xóa khỏi danh sách yêu thích của bạn.`,
      })
      return true
    } catch (error) {
      console.error("Error removing from wishlist:", error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa sản phẩm khỏi danh sách yêu thích.",
        variant: "destructive",
      })
      return false
    }
  }

  // Toggle product in wishlist
  const toggleWishlist = async (product: Product) => {
    if (isInWishlist(product.id)) {
      return await removeFromWishlist(product.id)
    } else {
      return await addToWishlist(product)
    }
  }

  // Clear wishlist
  const clearWishlist = async () => {
    const user = getUser()
    if (!user) return false

    try {
      setWishlist({ items: [], isLoading: false })
      saveWishlist([])
      
      toast({
        title: "Đã xóa danh sách yêu thích",
        description: "Tất cả sản phẩm đã được xóa khỏi danh sách yêu thích.",
      })
      return true
    } catch (error) {
      console.error("Error clearing wishlist:", error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa danh sách yêu thích.",
        variant: "destructive",
      })
      return false
    }
  }

  // Load wishlist on mount
  useEffect(() => {
    loadWishlist()
  }, [])

  return {
    wishlist: wishlist.items,
    isLoading: wishlist.isLoading,
    isInWishlist,
    addToWishlist,
    removeFromWishlist,
    toggleWishlist,
    clearWishlist,
    refreshWishlist: loadWishlist,
  }
}
