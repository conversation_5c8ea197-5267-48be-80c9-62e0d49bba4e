import { dbUtils } from './utils';
import { JsonUserModel, JsonProductModel, JsonCategoryModel, JsonTransactionModel, JsonReviewModel } from './json-models';
import { withTransaction } from './connection';

// Check if we should use JSON fallback - always use database utils now
const USE_JSON_FALLBACK = false;

// Database model interfaces
export interface DbCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  status: string;
  created_at: string;
}

export interface DbUser {
  id: string;
  name: string;
  email: string;
  password: string;
  phone?: string;
  role: 'admin' | 'customer';
  status: 'active' | 'inactive' | 'banned';
  created_at: string;
}

export interface DbUserAddress {
  id: number;
  user_id: string;
  full_name: string;
  phone: string;
  address: string;
  city: string;
  district: string;
  ward: string;
  is_default: boolean;
  created_at: string;
}

export interface DbProduct {
  id: string;
  name: string;
  description?: string;
  price: number;
  original_price?: number;
  image?: string;
  images?: string; // JSON string
  category_id: string;
  stock: number;
  rating: number;
  review_count: number;
  sold_count: number;
  featured: boolean;
  is_promotional: boolean;
  promotion_ends?: string;
  created_at: string;
}

export interface DbReview {
  id: string;
  product_id: string;
  user_id: string;
  rating: number;
  comment?: string;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  helpful_votes: number;
  unhelpful_votes: number;
  flag_reason?: string;
  rejection_reason?: string;
  created_at: string;
  approved_at?: string;
  rejected_at?: string;
  flagged_at?: string;
}

export interface DbOrder {
  id: string;
  user_id: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total_amount: number;
  payment_method?: string;
  created_at: string;
}

export interface DbOrderShippingAddress {
  id: number;
  order_id: string;
  full_name: string;
  phone: string;
  address: string;
  city: string;
  district: string;
  ward: string;
}

export interface DbCartItem {
  id: string;
  user_id: string;
  product_id: string;
  quantity: number;
  created_at: string;
  updated_at: string;
}

export interface DbOrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price: number;
  product_name: string;
  product_image?: string;
}

export interface DbTransaction {
  id: string;
  order_id: string;
  user_id: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  payment_method?: string;
  transaction_fee: number;
  refund_amount?: number;
  failure_reason?: string;
  refund_reason?: string;
  cancellation_reason?: string;
  created_at: string;
  completed_at?: string;
  failed_at?: string;
  refunded_at?: string;
  cancelled_at?: string;
}

// Model classes with CRUD operations
export class CategoryModel {
  static getAll(): DbCategory[] {
    if (USE_JSON_FALLBACK) {
      return JsonCategoryModel.getAll();
    }
    return dbUtils.select<DbCategory>('categories', {}, { orderBy: 'name' });
  }

  static getById(id: string): DbCategory | null {
    if (USE_JSON_FALLBACK) {
      return JsonCategoryModel.getById(id);
    }
    const results = dbUtils.select<DbCategory>('categories', { id });
    return results[0] || null;
  }

  static getBySlug(slug: string): DbCategory | null {
    if (USE_JSON_FALLBACK) {
      return JsonCategoryModel.getBySlug(slug);
    }
    const results = dbUtils.select<DbCategory>('categories', { slug });
    return results[0] || null;
  }

  static create(data: Omit<DbCategory, 'created_at'>): DbCategory {
    if (USE_JSON_FALLBACK) {
      return JsonCategoryModel.create(data);
    }
    return dbUtils.insert<DbCategory>('categories', {
      ...data,
      created_at: new Date().toISOString()
    });
  }

  static update(id: string, data: Partial<DbCategory>): DbCategory | null {
    if (USE_JSON_FALLBACK) {
      return JsonCategoryModel.update(id, data);
    }
    return dbUtils.update<DbCategory>('categories', data, { id });
  }

  static delete(id: string): boolean {
    if (USE_JSON_FALLBACK) {
      return JsonCategoryModel.delete(id);
    }
    return dbUtils.delete('categories', { id });
  }
}

export class UserModel {
  static getAll(): DbUser[] {
    if (USE_JSON_FALLBACK) {
      return JsonUserModel.getAll();
    }
    return dbUtils.select<DbUser>('users', {}, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getById(id: string): DbUser | null {
    if (USE_JSON_FALLBACK) {
      return JsonUserModel.getById(id);
    }
    const results = dbUtils.select<DbUser>('users', { id });
    return results[0] || null;
  }

  static getByEmail(email: string): DbUser | null {
    if (USE_JSON_FALLBACK) {
      return JsonUserModel.getByEmail(email);
    }
    const results = dbUtils.select<DbUser>('users', { email });
    return results[0] || null;
  }

  static create(data: Omit<DbUser, 'created_at'>): DbUser {
    if (USE_JSON_FALLBACK) {
      return JsonUserModel.create(data);
    }
    return dbUtils.insert<DbUser>('users', {
      ...data,
      created_at: new Date().toISOString()
    });
  }

  static update(id: string, data: Partial<DbUser>): DbUser | null {
    if (USE_JSON_FALLBACK) {
      return JsonUserModel.update(id, data);
    }
    return dbUtils.update<DbUser>('users', data, { id });
  }

  static delete(id: string): boolean {
    if (USE_JSON_FALLBACK) {
      return JsonUserModel.delete(id);
    }
    return dbUtils.delete('users', { id });
  }

  static getUserWithAddress(userId: string): (DbUser & { address?: DbUserAddress }) | null {
    const user = this.getById(userId);
    if (!user) return null;

    const addresses = dbUtils.select<DbUserAddress>('user_addresses', { user_id: userId });
    const defaultAddress = addresses.find(addr => addr.is_default) || addresses[0];

    return {
      ...user,
      address: defaultAddress
    };
  }
}

export class UserAddressModel {
  static getByUserId(userId: string): DbUserAddress[] {
    return dbUtils.select<DbUserAddress>('user_addresses', { user_id: userId });
  }

  static create(data: Omit<DbUserAddress, 'id' | 'created_at'>): DbUserAddress {
    return dbUtils.insert<DbUserAddress>('user_addresses', {
      ...data,
      created_at: new Date().toISOString()
    });
  }

  static update(id: number, data: Partial<DbUserAddress>): DbUserAddress | null {
    return dbUtils.update<DbUserAddress>('user_addresses', data, { id });
  }

  static delete(id: number): boolean {
    return dbUtils.delete('user_addresses', { id });
  }
}

export class ProductModel {
  static getAll(options: {
    featured?: boolean;
    isPromotional?: boolean;
    categoryId?: string;
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'ASC' | 'DESC';
  } = {}): DbProduct[] {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.getAll(options);
    }

    const { featured, isPromotional, categoryId, ...queryOptions } = options;
    const where: Record<string, any> = {};

    if (featured !== undefined) where.featured = featured;
    if (isPromotional !== undefined) where.is_promotional = isPromotional;
    if (categoryId) where.category_id = categoryId;

    return dbUtils.select<DbProduct>('products', where, queryOptions);
  }

  static getById(id: string): DbProduct | null {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.getById(id);
    }
    const results = dbUtils.select<DbProduct>('products', { id });
    return results[0] || null;
  }

  static getFeatured(limit?: number): DbProduct[] {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.getFeatured(limit);
    }
    return this.getAll({ featured: true, limit, orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getPromotional(limit?: number): DbProduct[] {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.getPromotional(limit);
    }
    return this.getAll({ isPromotional: true, limit, orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getByCategoryId(categoryId: string, limit?: number): DbProduct[] {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.getByCategoryId(categoryId, limit);
    }
    return this.getAll({ categoryId, limit });
  }

  static create(data: Omit<DbProduct, 'created_at'>): DbProduct {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.create(data);
    }
    return dbUtils.insert<DbProduct>('products', {
      ...data,
      created_at: new Date().toISOString()
    });
  }

  static update(id: string, data: Partial<DbProduct>): DbProduct | null {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.update(id, data);
    }
    return dbUtils.update<DbProduct>('products', data, { id });
  }

  static delete(id: string): boolean {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.delete(id);
    }
    return dbUtils.delete('products', { id });
  }

  static search(query: string, limit?: number): DbProduct[] {
    if (USE_JSON_FALLBACK) {
      return JsonProductModel.search(query, limit);
    }
    const sql = `
      SELECT * FROM products
      WHERE name LIKE ? OR description LIKE ?
      ORDER BY featured DESC, created_at DESC
      ${limit ? `LIMIT ${limit}` : ''}
    `;
    const searchTerm = `%${query}%`;
    return dbUtils.raw<DbProduct>(sql, [searchTerm, searchTerm]);
  }
}

export class ReviewModel {
  static getAll(): DbReview[] {
    if (USE_JSON_FALLBACK) {
      return JsonReviewModel.getAll();
    }
    return dbUtils.select<DbReview>('reviews', {}, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getById(id: string): DbReview | null {
    if (USE_JSON_FALLBACK) {
      return JsonReviewModel.getById(id);
    }
    const results = dbUtils.select<DbReview>('reviews', { id });
    return results[0] || null;
  }

  static getByProductId(productId: string): DbReview[] {
    if (USE_JSON_FALLBACK) {
      return JsonReviewModel.getByProductId(productId);
    }
    return dbUtils.select<DbReview>('reviews', { product_id: productId }, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getByUserId(userId: string): DbReview[] {
    if (USE_JSON_FALLBACK) {
      return JsonReviewModel.getByUserId(userId);
    }
    return dbUtils.select<DbReview>('reviews', { user_id: userId }, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getByStatus(status: string): DbReview[] {
    if (USE_JSON_FALLBACK) {
      return JsonReviewModel.getByStatus(status);
    }
    return dbUtils.select<DbReview>('reviews', { status }, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static create(data: Omit<DbReview, 'created_at'>): DbReview {
    if (USE_JSON_FALLBACK) {
      return JsonReviewModel.create(data);
    }
    return dbUtils.insert<DbReview>('reviews', {
      ...data,
      created_at: new Date().toISOString()
    });
  }

  static update(id: string, data: Partial<DbReview>): DbReview | null {
    if (USE_JSON_FALLBACK) {
      return JsonReviewModel.update(id, data);
    }
    return dbUtils.update<DbReview>('reviews', data, { id });
  }

  static delete(id: string): boolean {
    if (USE_JSON_FALLBACK) {
      return JsonReviewModel.delete(id);
    }
    return dbUtils.delete('reviews', { id });
  }
}

export class OrderModel {
  static getAll(): DbOrder[] {
    return dbUtils.select<DbOrder>('orders', {}, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getById(id: string): DbOrder | null {
    const results = dbUtils.select<DbOrder>('orders', { id });
    return results[0] || null;
  }

  static getByUserId(userId: string): DbOrder[] {
    return dbUtils.select<DbOrder>('orders', { user_id: userId }, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getByStatus(status: string): DbOrder[] {
    return dbUtils.select<DbOrder>('orders', { status }, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static create(data: Omit<DbOrder, 'created_at'>): DbOrder {
    return dbUtils.insert<DbOrder>('orders', {
      ...data,
      created_at: new Date().toISOString()
    });
  }

  static update(id: string, data: Partial<DbOrder>): DbOrder | null {
    return dbUtils.update<DbOrder>('orders', data, { id });
  }

  static delete(id: string): boolean {
    return dbUtils.delete('orders', { id });
  }

  static getOrderWithDetails(orderId: string): (DbOrder & {
    items: DbOrderItem[],
    shippingAddress: DbOrderShippingAddress | null
  }) | null {
    const order = this.getById(orderId);
    if (!order) return null;

    const items = OrderItemModel.getByOrderId(orderId);
    const shippingAddress = OrderShippingAddressModel.getByOrderId(orderId);

    return {
      ...order,
      items,
      shippingAddress
    };
  }
}

export class OrderItemModel {
  static getByOrderId(orderId: string): DbOrderItem[] {
    return dbUtils.select<DbOrderItem>('order_items', { order_id: orderId });
  }

  static getById(id: string): DbOrderItem | null {
    const results = dbUtils.select<DbOrderItem>('order_items', { id });
    return results[0] || null;
  }

  static create(data: DbOrderItem): DbOrderItem {
    return dbUtils.insert<DbOrderItem>('order_items', data);
  }

  static update(id: string, data: Partial<DbOrderItem>): DbOrderItem | null {
    return dbUtils.update<DbOrderItem>('order_items', data, { id });
  }

  static delete(id: string): boolean {
    return dbUtils.delete('order_items', { id });
  }

  static deleteByOrderId(orderId: string): boolean {
    return dbUtils.delete('order_items', { order_id: orderId });
  }
}

export class OrderShippingAddressModel {
  static getByOrderId(orderId: string): DbOrderShippingAddress | null {
    const results = dbUtils.select<DbOrderShippingAddress>('order_shipping_addresses', { order_id: orderId });
    return results[0] || null;
  }

  static create(data: Omit<DbOrderShippingAddress, 'id'>): DbOrderShippingAddress {
    return dbUtils.insert<DbOrderShippingAddress>('order_shipping_addresses', data);
  }

  static update(id: number, data: Partial<DbOrderShippingAddress>): DbOrderShippingAddress | null {
    return dbUtils.update<DbOrderShippingAddress>('order_shipping_addresses', data, { id });
  }

  static delete(id: number): boolean {
    return dbUtils.delete('order_shipping_addresses', { id });
  }

  static deleteByOrderId(orderId: string): boolean {
    return dbUtils.delete('order_shipping_addresses', { order_id: orderId });
  }
}

export class TransactionModel {
  static getAll(): DbTransaction[] {
    if (USE_JSON_FALLBACK) {
      return JsonTransactionModel.getAll();
    }
    return dbUtils.select<DbTransaction>('transactions', {}, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getById(id: string): DbTransaction | null {
    if (USE_JSON_FALLBACK) {
      return JsonTransactionModel.getById(id);
    }
    const results = dbUtils.select<DbTransaction>('transactions', { id });
    return results[0] || null;
  }

  static getByOrderId(orderId: string): DbTransaction[] {
    if (USE_JSON_FALLBACK) {
      return JsonTransactionModel.getByOrderId(orderId);
    }
    return dbUtils.select<DbTransaction>('transactions', { order_id: orderId });
  }

  static getByUserId(userId: string): DbTransaction[] {
    if (USE_JSON_FALLBACK) {
      return JsonTransactionModel.getByUserId(userId);
    }
    return dbUtils.select<DbTransaction>('transactions', { user_id: userId }, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getByStatus(status: string): DbTransaction[] {
    if (USE_JSON_FALLBACK) {
      return JsonTransactionModel.getByStatus(status);
    }
    return dbUtils.select<DbTransaction>('transactions', { status }, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static create(data: Omit<DbTransaction, 'created_at'>): DbTransaction {
    if (USE_JSON_FALLBACK) {
      return JsonTransactionModel.create(data);
    }
    return dbUtils.insert<DbTransaction>('transactions', {
      ...data,
      created_at: new Date().toISOString()
    });
  }

  static update(id: string, data: Partial<DbTransaction>): DbTransaction | null {
    if (USE_JSON_FALLBACK) {
      return JsonTransactionModel.update(id, data);
    }
    return dbUtils.update<DbTransaction>('transactions', data, { id });
  }

  static delete(id: string): boolean {
    if (USE_JSON_FALLBACK) {
      return JsonTransactionModel.delete(id);
    }
    return dbUtils.delete('transactions', { id });
  }
}

export class CartModel {
  static getByUserId(userId: string): DbCartItem[] {
    return dbUtils.select<DbCartItem>('cart_items', { user_id: userId }, { orderBy: 'created_at', orderDirection: 'DESC' });
  }

  static getById(id: string): DbCartItem | null {
    const results = dbUtils.select<DbCartItem>('cart_items', { id });
    return results[0] || null;
  }

  static getByUserAndProduct(userId: string, productId: string): DbCartItem | null {
    const results = dbUtils.select<DbCartItem>('cart_items', { user_id: userId, product_id: productId });
    return results[0] || null;
  }

  static create(data: Omit<DbCartItem, 'created_at' | 'updated_at'>): DbCartItem {
    const now = new Date().toISOString();
    return dbUtils.insert<DbCartItem>('cart_items', {
      ...data,
      created_at: now,
      updated_at: now
    });
  }

  static update(id: string, data: Partial<DbCartItem>): DbCartItem | null {
    return dbUtils.update<DbCartItem>('cart_items', {
      ...data,
      updated_at: new Date().toISOString()
    }, { id });
  }

  static updateQuantity(id: string, quantity: number): DbCartItem | null {
    return this.update(id, { quantity });
  }

  static delete(id: string): boolean {
    return dbUtils.delete('cart_items', { id });
  }

  static deleteByUserId(userId: string): boolean {
    return dbUtils.delete('cart_items', { user_id: userId });
  }

  static deleteByUserAndProduct(userId: string, productId: string): boolean {
    return dbUtils.delete('cart_items', { user_id: userId, product_id: productId });
  }

  static getCartWithProducts(userId: string): (DbCartItem & { product: DbProduct })[] {
    const cartItems = this.getByUserId(userId);
    return cartItems.map(item => {
      const product = ProductModel.getById(item.product_id);
      return {
        ...item,
        product: product!
      };
    }).filter(item => item.product); // Filter out items where product doesn't exist
  }

  static getTotalItems(userId: string): number {
    const cartItems = this.getByUserId(userId);
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  }

  static getTotalAmount(userId: string): number {
    const cartWithProducts = this.getCartWithProducts(userId);
    return cartWithProducts.reduce((total, item) => total + (item.product.price * item.quantity), 0);
  }
}

// Export transaction helper
export { withTransaction };
