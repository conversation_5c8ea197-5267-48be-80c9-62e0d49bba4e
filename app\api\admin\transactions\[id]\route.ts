import { NextResponse } from "next/server"
import { TransactionModel, OrderModel, UserModel } from "../../../../../lib/database/models"

// GET /api/admin/transactions/[id]
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const transaction = TransactionModel.getById(id)

    if (!transaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      )
    }

    // Get related order and user information
    const order = OrderModel.getById(transaction.order_id)
    const user = UserModel.getById(transaction.user_id)

    // Get order items if order exists
    let orderItems = []
    if (order) {
      // Get order items from the database
      const { dbUtils } = require("../../../../../lib/database/utils")
      orderItems = dbUtils.select('order_items', { order_id: order.id })
    }

    // Get shipping address if order exists
    let shippingAddress = null
    if (order) {
      const { dbUtils } = require("../../../../../lib/database/utils")
      const addresses = dbUtils.select('order_shipping_addresses', { order_id: order.id })
      shippingAddress = addresses[0] || null
    }

    const enrichedTransaction = {
      id: transaction.id,
      orderId: transaction.order_id,
      userId: transaction.user_id,
      amount: transaction.amount,
      status: transaction.status,
      paymentMethod: transaction.payment_method,
      transactionFee: transaction.transaction_fee || 0,
      refundAmount: transaction.refund_amount,
      failureReason: transaction.failure_reason,
      refundReason: transaction.refund_reason,
      cancellationReason: transaction.cancellation_reason,
      createdAt: transaction.created_at,
      completedAt: transaction.completed_at,
      failedAt: transaction.failed_at,
      refundedAt: transaction.refunded_at,
      cancelledAt: transaction.cancelled_at,
      order: order ? {
        id: order.id,
        status: order.status,
        totalAmount: order.total_amount,
        paymentMethod: order.payment_method,
        createdAt: order.created_at,
        items: orderItems.map(item => ({
          id: item.id,
          productId: item.product_id,
          productName: item.product_name,
          productImage: item.product_image,
          quantity: item.quantity,
          price: item.price
        })),
        shippingAddress: shippingAddress ? {
          fullName: shippingAddress.full_name,
          phone: shippingAddress.phone,
          address: shippingAddress.address,
          city: shippingAddress.city,
          district: shippingAddress.district,
          ward: shippingAddress.ward
        } : null
      } : null,
      user: user ? {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role
      } : null
    }

    return NextResponse.json(enrichedTransaction)
  } catch (error) {
    console.error("Error fetching transaction:", error)
    return NextResponse.json(
      { error: "Failed to fetch transaction" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/transactions/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status } = await request.json()

    const updatedTransaction = TransactionModel.update(id, { status })

    if (!updatedTransaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      id: updatedTransaction.id,
      orderId: updatedTransaction.order_id,
      userId: updatedTransaction.user_id,
      amount: updatedTransaction.amount,
      status: updatedTransaction.status,
      paymentMethod: updatedTransaction.payment_method,
      transactionFee: updatedTransaction.transaction_fee,
      refundAmount: updatedTransaction.refund_amount,
      failureReason: updatedTransaction.failure_reason,
      refundReason: updatedTransaction.refund_reason,
      cancellationReason: updatedTransaction.cancellation_reason,
      createdAt: updatedTransaction.created_at,
      completedAt: updatedTransaction.completed_at,
      failedAt: updatedTransaction.failed_at,
      refundedAt: updatedTransaction.refunded_at,
      cancelledAt: updatedTransaction.cancelled_at
    })
  } catch (error) {
    console.error("Error updating transaction:", error)
    return NextResponse.json(
      { error: "Failed to update transaction" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/transactions/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const deleted = TransactionModel.delete(id)

    if (!deleted) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: "Transaction deleted successfully" })
  } catch (error) {
    console.error("Error deleting transaction:", error)
    return NextResponse.json(
      { error: "Failed to delete transaction" },
      { status: 500 }
    )
  }
}
